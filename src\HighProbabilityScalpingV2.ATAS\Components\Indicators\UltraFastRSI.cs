using System;
using System.Collections.Generic;
using HighProbabilityScalpingV2.ATAS.Components.Interfaces;
using HighProbabilityScalpingV2.ATAS.Components.Models;
using HighProbabilityScalpingV2.ATAS.Components.Utils;

namespace HighProbabilityScalpingV2.ATAS.Components.Indicators
{
    /// <summary>
    /// Ultra-Fast RSI (Relative Strength Index) indicator implementation
    /// Optimized for ultra-low timeframe scalping with 3-period calculation
    /// Phase 2 indicator with 25% weight in signal generation
    /// </summary>
    public class UltraFastRSI : IIndicator
    {
        private readonly CircularBuffer<decimal> _prices;
        private readonly CircularBuffer<decimal> _gains;
        private readonly CircularBuffer<decimal> _losses;
        private readonly CircularBuffer<decimal> _rsiValues;
        private readonly int _period;
        private readonly decimal _overboughtLevel;
        private readonly decimal _oversoldLevel;
        private readonly PriceSource _priceSource;

        // Performance tracking
        private DateTime _lastCalculationTime;
        private TimeSpan _lastCalculationDuration;
        private long _calculationCount;

        // IIndicator interface properties
        private decimal _weight = 0.25m; // 25% weight for Phase 2
        private bool _isEnabled = true;

        /// <summary>
        /// Creates a new Ultra-Fast RSI indicator
        /// </summary>
        /// <param name="period">Period for RSI calculation (default: 3 for ultra-fast)</param>
        /// <param name="overboughtLevel">Overbought threshold (default: 80)</param>
        /// <param name="oversoldLevel">Oversold threshold (default: 20)</param>
        /// <param name="priceSource">Price source for calculation (default: Close)</param>
        public UltraFastRSI(int period = 3, decimal overboughtLevel = 80m, decimal oversoldLevel = 20m, 
            PriceSource priceSource = PriceSource.Close)
        {
            if (period < 2)
                throw new ArgumentException("Period must be at least 2", nameof(period));
            if (overboughtLevel <= oversoldLevel)
                throw new ArgumentException("Overbought level must be greater than oversold level");
            if (overboughtLevel > 100 || oversoldLevel < 0)
                throw new ArgumentException("RSI levels must be between 0 and 100");

            _period = period;
            _overboughtLevel = overboughtLevel;
            _oversoldLevel = oversoldLevel;
            _priceSource = priceSource;

            // Initialize circular buffers
            _prices = new CircularBuffer<decimal>(period + 1); // +1 for price change calculation
            _gains = new CircularBuffer<decimal>(period);
            _losses = new CircularBuffer<decimal>(period);
            _rsiValues = new CircularBuffer<decimal>(100); // Keep history for trend analysis

            _calculationCount = 0;
        }

        /// <summary>
        /// Calculates RSI value and generates trading signal (IIndicator interface)
        /// </summary>
        /// <param name="candle">Current candle data</param>
        /// <param name="marketData">Additional market data (unused in Phase 2)</param>
        /// <returns>Trading signal with confidence level</returns>
        public IndicatorSignal Calculate(IndicatorCandle candle, MarketData marketData)
        {
            var startTime = DateTime.UtcNow;
            _calculationCount++;

            try
            {
                if (!IsEnabled)
                {
                    return IndicatorSignal.Neutral("Indicator disabled", Name);
                }

                // Get price based on source
                var price = GetPrice(candle, _priceSource);
                _prices.Add(price);

                // Check if we have enough data
                if (!IsReady)
                {
                    return IndicatorSignal.Neutral("Insufficient data for RSI calculation", Name);
                }

                // Calculate RSI
                var rsi = CalculateRSI();
                _rsiValues.Add(rsi);

                // Generate signal
                var signal = GenerateSignal(rsi, candle);

                // Add metadata
                signal.Metadata["RSI"] = rsi;
                signal.Metadata["Period"] = _period;
                signal.Metadata["OverboughtLevel"] = _overboughtLevel;
                signal.Metadata["OversoldLevel"] = _oversoldLevel;
                signal.Metadata["PriceSource"] = _priceSource.ToString();

                return signal;
            }
            finally
            {
                // Track performance
                _lastCalculationDuration = DateTime.UtcNow - startTime;
                _lastCalculationTime = DateTime.UtcNow;
            }
        }

        /// <summary>
        /// Calculates the RSI value using the standard formula
        /// </summary>
        /// <returns>RSI value (0-100)</returns>
        private decimal CalculateRSI()
        {
            // Calculate gains and losses for current period
            for (int i = 1; i < _prices.Count; i++)
            {
                var priceChange = _prices[i] - _prices[i - 1];
                
                if (priceChange > 0)
                {
                    _gains.Add(priceChange);
                    _losses.Add(0);
                }
                else
                {
                    _gains.Add(0);
                    _losses.Add(Math.Abs(priceChange));
                }
            }

            // Calculate average gain and loss
            var avgGain = _gains.Average();
            var avgLoss = _losses.Average();

            // Avoid division by zero
            if (avgLoss == 0)
                return 100; // All gains, maximum RSI

            // Calculate RS and RSI
            var rs = avgGain / avgLoss;
            var rsi = 100 - (100 / (1 + rs));

            return rsi;
        }

        /// <summary>
        /// Generates trading signal based on RSI value
        /// </summary>
        /// <param name="rsi">Current RSI value</param>
        /// <param name="candle">Current candle for context</param>
        /// <returns>Trading signal</returns>
        private IndicatorSignal GenerateSignal(decimal rsi, IndicatorCandle candle)
        {
            var reason = $"RSI: {rsi:F1}";

            // Sell signal (overbought)
            if (rsi >= _overboughtLevel)
            {
                var confidence = CalculateOverboughtConfidence(rsi);
                return IndicatorSignal.Sell(confidence, reason, Name, rsi);
            }

            // Buy signal (oversold)
            if (rsi <= _oversoldLevel)
            {
                var confidence = CalculateOversoldConfidence(rsi);
                return IndicatorSignal.Buy(confidence, reason, Name, rsi);
            }

            // Neutral signal
            return IndicatorSignal.Neutral(reason, Name);
        }

        /// <summary>
        /// Calculates confidence for overbought (sell) signals
        /// </summary>
        /// <param name="rsi">Current RSI value</param>
        /// <returns>Confidence level (0.0 to 1.0)</returns>
        private decimal CalculateOverboughtConfidence(decimal rsi)
        {
            // Base confidence starts at 60% when overbought threshold is met
            var baseConfidence = 0.6m;
            
            // Additional confidence based on how far above overbought level
            var excessValue = rsi - _overboughtLevel;
            var bonusConfidence = Math.Min(0.30m, excessValue * 0.015m); // Max 30% bonus
            
            var totalConfidence = baseConfidence + bonusConfidence;
            
            // Apply trend boost if available
            if (_rsiValues.Count >= 3)
            {
                var trendBoost = CalculateTrendBoost(false); // Sell signal
                totalConfidence += trendBoost;
            }

            return Math.Min(0.95m, totalConfidence); // Cap at 95%
        }

        /// <summary>
        /// Calculates confidence for oversold (buy) signals
        /// </summary>
        /// <param name="rsi">Current RSI value</param>
        /// <returns>Confidence level (0.0 to 1.0)</returns>
        private decimal CalculateOversoldConfidence(decimal rsi)
        {
            // Base confidence starts at 60% when oversold threshold is met
            var baseConfidence = 0.6m;
            
            // Additional confidence based on how far below oversold level
            var excessValue = _oversoldLevel - rsi;
            var bonusConfidence = Math.Min(0.30m, excessValue * 0.015m); // Max 30% bonus
            
            var totalConfidence = baseConfidence + bonusConfidence;
            
            // Apply trend boost if available
            if (_rsiValues.Count >= 3)
            {
                var trendBoost = CalculateTrendBoost(true); // Buy signal
                totalConfidence += trendBoost;
            }

            return Math.Min(0.95m, totalConfidence); // Cap at 95%
        }

        /// <summary>
        /// Calculates trend boost for confidence
        /// </summary>
        /// <param name="isBuySignal">Whether this is for a buy signal</param>
        /// <returns>Trend boost value</returns>
        private decimal CalculateTrendBoost(bool isBuySignal)
        {
            if (_rsiValues.Count < 3)
                return 0;

            var current = _rsiValues.Latest;
            var previous = _rsiValues[_rsiValues.Count - 2];
            var beforePrevious = _rsiValues[_rsiValues.Count - 3];

            // Check for consistent trend
            if (isBuySignal)
            {
                // For buy signals, look for RSI moving up from oversold
                if (current > previous && previous >= beforePrevious && current <= _oversoldLevel + 10)
                    return 0.05m; // 5% boost for uptrend from oversold
            }
            else
            {
                // For sell signals, look for RSI moving down from overbought
                if (current < previous && previous <= beforePrevious && current >= _overboughtLevel - 10)
                    return 0.05m; // 5% boost for downtrend from overbought
            }

            return 0;
        }

        /// <summary>
        /// Gets price value based on specified source
        /// </summary>
        /// <param name="candle">Candle data</param>
        /// <param name="source">Price source</param>
        /// <returns>Price value</returns>
        private decimal GetPrice(IndicatorCandle candle, PriceSource source)
        {
            return source switch
            {
                PriceSource.Open => candle.Open,
                PriceSource.High => candle.High,
                PriceSource.Low => candle.Low,
                PriceSource.Close => candle.Close,
                PriceSource.Typical => candle.TypicalPrice,
                PriceSource.Median => candle.MedianPrice,
                PriceSource.WeightedClose => candle.WeightedClose,
                _ => candle.Close
            };
        }

        /// <summary>
        /// Resets the indicator state and clears all historical data
        /// </summary>
        public void Reset()
        {
            _prices.Clear();
            _gains.Clear();
            _losses.Clear();
            _rsiValues.Clear();
            _calculationCount = 0;
            _lastCalculationTime = DateTime.MinValue;
            _lastCalculationDuration = TimeSpan.Zero;
        }

        #region IIndicator Interface Implementation

        /// <summary>
        /// Name of the indicator
        /// </summary>
        public string Name => "UltraFastRSI";

        /// <summary>
        /// Category of the indicator
        /// </summary>
        public string Category => "Momentum";

        /// <summary>
        /// Weight of this indicator in signal aggregation
        /// </summary>
        public decimal Weight 
        { 
            get => _weight; 
            set => _weight = Math.Max(0, Math.Min(1, value)); 
        }

        /// <summary>
        /// Whether this indicator is enabled for calculations
        /// </summary>
        public bool IsEnabled 
        { 
            get => _isEnabled; 
            set => _isEnabled = value; 
        }

        /// <summary>
        /// Whether the indicator has enough data to generate reliable signals
        /// </summary>
        public bool IsReady => _prices.Count >= _period + 1; // Need extra for price changes

        /// <summary>
        /// Duration of the last calculation for performance monitoring
        /// </summary>
        public TimeSpan LastCalculationTime => _lastCalculationDuration;

        /// <summary>
        /// Gets current operational status of the indicator
        /// </summary>
        /// <returns>Status information for monitoring and debugging</returns>
        public IndicatorStatus GetStatus()
        {
            return new IndicatorStatus
            {
                IsOperational = IsEnabled && IsReady,
                StatusMessage = IsReady ? "Ready" : "Insufficient data",
                DataPointsRequired = _period + 1,
                DataPointsAvailable = _prices.Count,
                LastUpdate = _lastCalculationTime,
                Metadata = new Dictionary<string, object>
                {
                    ["CurrentRSI"] = _rsiValues.Count > 0 ? _rsiValues.Latest : 0,
                    ["CalculationCount"] = _calculationCount,
                    ["LastCalculationTimeMs"] = LastCalculationTime.TotalMilliseconds,
                    ["OverboughtLevel"] = _overboughtLevel,
                    ["OversoldLevel"] = _oversoldLevel,
                    ["PriceSource"] = _priceSource.ToString(),
                    ["Weight"] = Weight,
                    ["IsEnabled"] = IsEnabled
                }
            };
        }

        #endregion

        #region Public Properties

        /// <summary>
        /// Current RSI value (latest calculated)
        /// </summary>
        public decimal CurrentValue => _rsiValues.Count > 0 ? _rsiValues.Latest : 0;

        /// <summary>
        /// Number of calculations performed
        /// </summary>
        public long CalculationCount => _calculationCount;

        /// <summary>
        /// Timestamp of last update
        /// </summary>
        public DateTime LastUpdateTime => _lastCalculationTime;

        /// <summary>
        /// Indicator configuration
        /// </summary>
        public int Period => _period;
        public decimal OverboughtLevel => _overboughtLevel;
        public decimal OversoldLevel => _oversoldLevel;
        public PriceSource PriceSource => _priceSource;

        #endregion
    }
}
