{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\Smart Trading\\tests\\HighProbabilityScalpingV2.Tests\\HighProbabilityScalpingV2.Tests.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\Smart Trading\\src\\HighProbabilityScalpingV2.ATAS\\HighProbabilityScalpingV2.ATAS.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Smart Trading\\src\\HighProbabilityScalpingV2.ATAS\\HighProbabilityScalpingV2.ATAS.csproj", "projectName": "HighProbabilityScalpingV2.ATAS", "projectPath": "C:\\Users\\<USER>\\Desktop\\Smart Trading\\src\\HighProbabilityScalpingV2.ATAS\\HighProbabilityScalpingV2.ATAS.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Smart Trading\\src\\HighProbabilityScalpingV2.ATAS\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300-preview.0.25177.5/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Smart Trading\\tests\\HighProbabilityScalpingV2.Tests\\HighProbabilityScalpingV2.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Smart Trading\\tests\\HighProbabilityScalpingV2.Tests\\HighProbabilityScalpingV2.Tests.csproj", "projectName": "HighProbabilityScalpingV2.Tests", "projectPath": "C:\\Users\\<USER>\\Desktop\\Smart Trading\\tests\\HighProbabilityScalpingV2.Tests\\HighProbabilityScalpingV2.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Smart Trading\\tests\\HighProbabilityScalpingV2.Tests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Smart Trading\\src\\HighProbabilityScalpingV2.ATAS\\HighProbabilityScalpingV2.ATAS.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Smart Trading\\src\\HighProbabilityScalpingV2.ATAS\\HighProbabilityScalpingV2.ATAS.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"MSTest.TestAdapter": {"target": "Package", "version": "[3.1.1, )"}, "MSTest.TestFramework": {"target": "Package", "version": "[3.1.1, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.8.0, )"}, "coverlet.collector": {"target": "Package", "version": "[6.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300-preview.0.25177.5/PortableRuntimeIdentifierGraph.json"}}}}}