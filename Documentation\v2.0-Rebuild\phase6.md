# Phase 6: Trading Integration & Order Management
# High-Probability Scalping Strategy v2.0

**Phase Duration:** Week 6 (7 days)  
**Status:** Planning  
**Priority:** Trading Execution  
**Dependencies:** Phase 5 Complete  

---

## 🎯 **PHASE 6 OBJECTIVES**

Implement the complete trading execution system that converts high-confidence signals into actual trades with proper risk management:
- Order management and execution system
- Position tracking and lifecycle management
- TP/SL automation with proper cleanup
- Risk management and position sizing
- Trade outcome tracking and feedback

### **Success Criteria:**
- ✅ Order management system operational with ATAS integration
- ✅ Position tracking working with MaxPositions=1 enforcement
- ✅ TP/SL orders placed and managed automatically
- ✅ Risk management preventing over-exposure
- ✅ Trade outcomes feeding back to adaptive system

---

## 📋 **DETAILED TASK BREAKDOWN**

### **Day 1: Order Management System Architecture**

#### **Task 6.1: Design Core Order Management System**
**Estimated Time:** 4 hours  
**Assignee:** Trading Systems Developer  

**OrderManager Implementation:**
```csharp
public class OrderManager
{
    private readonly SimpleLogger _logger;
    private readonly PerformanceMonitor _performance;
    private readonly RiskManager _riskManager;
    private readonly PositionTracker _positionTracker;
    private readonly OrderCleanupSystem _cleanupSystem;
    private readonly ChartStrategy _strategy;
    
    // Thread-safe order tracking
    private readonly object _orderLock = new object();
    private readonly Dictionary<string, ManagedOrder> _activeOrders;
    private readonly Dictionary<string, Position> _activePositions;
    
    public OrderManager(ChartStrategy strategy, SimpleLogger logger, PerformanceMonitor performance)
    {
        _strategy = strategy;
        _logger = logger;
        _performance = performance;
        _riskManager = new RiskManager(logger);
        _positionTracker = new PositionTracker(logger);
        _cleanupSystem = new OrderCleanupSystem(logger);
        _activeOrders = new Dictionary<string, ManagedOrder>();
        _activePositions = new Dictionary<string, Position>();
    }
    
    public OrderExecutionResult ProcessTradingSignal(CoordinatedSignal signal, TradingParameters parameters)
    {
        using (_performance.StartTiming("OrderManager_ProcessSignal"))
        {
            // Step 1: Risk validation
            var riskCheck = _riskManager.ValidateSignal(signal, parameters, _activePositions.Values.ToList());
            if (!riskCheck.Approved)
            {
                return OrderExecutionResult.Rejected(riskCheck.Reason);
            }
            
            // Step 2: Position limit check
            if (_activePositions.Count >= parameters.MaxPositions)
            {
                return OrderExecutionResult.Rejected($"Maximum positions reached: {parameters.MaxPositions}");
            }
            
            // Step 3: Calculate position size
            var positionSize = _riskManager.CalculatePositionSize(signal, parameters);
            
            // Step 4: Execute market order
            var marketOrderResult = ExecuteMarketOrder(signal, positionSize, parameters);
            if (!marketOrderResult.Success)
            {
                return OrderExecutionResult.Failed(marketOrderResult.ErrorMessage);
            }
            
            // Step 5: Place TP/SL orders
            var protectionResult = PlaceProtectionOrders(marketOrderResult.Order, parameters);
            
            return new OrderExecutionResult
            {
                Success = true,
                MarketOrder = marketOrderResult.Order,
                TakeProfitOrder = protectionResult.TakeProfitOrder,
                StopLossOrder = protectionResult.StopLossOrder,
                PositionSize = positionSize,
                ExecutionTime = _performance.GetLastTiming("OrderManager_ProcessSignal"),
                Timestamp = DateTime.UtcNow
            };
        }
    }
    
    private MarketOrderResult ExecuteMarketOrder(CoordinatedSignal signal, decimal quantity, TradingParameters parameters)
    {
        try
        {
            var orderSide = signal.Direction == SignalDirection.Buy ? OrderSide.Buy : OrderSide.Sell;
            var orderComment = $"HPSS-{signal.Direction}-{DateTime.UtcNow:HHmmss}";
            
            var order = _strategy.SubmitOrder(new OrderRequest
            {
                Type = OrderType.Market,
                Side = orderSide,
                Quantity = quantity,
                Comment = orderComment,
                TimeInForce = TimeInForce.IOC
            });
            
            if (order != null)
            {
                lock (_orderLock)
                {
                    _activeOrders[order.Id] = new ManagedOrder
                    {
                        Order = order,
                        Signal = signal,
                        OrderType = ManagedOrderType.Market,
                        CreatedAt = DateTime.UtcNow
                    };
                }
                
                _logger.LogInfo($"🎯 Market order submitted: {orderSide} {quantity} @ Market - {orderComment}");
                return MarketOrderResult.Success(order);
            }
            
            return MarketOrderResult.Failed("Order submission returned null");
        }
        catch (Exception ex)
        {
            _logger.LogError($"❌ Market order execution failed: {ex.Message}");
            return MarketOrderResult.Failed(ex.Message);
        }
    }
    
    private ProtectionOrderResult PlaceProtectionOrders(Order marketOrder, TradingParameters parameters)
    {
        try
        {
            var currentPrice = _strategy.Instrument.MasterInstrument.Round2TickSize(_strategy.GetCurrentBid());
            var isLong = marketOrder.Side == OrderSide.Buy;
            
            // Calculate TP/SL prices
            var takeProfitPrice = isLong ? 
                currentPrice * (1 + parameters.TakeProfitPercent / 100m) :
                currentPrice * (1 - parameters.TakeProfitPercent / 100m);
            
            var stopLossPrice = isLong ?
                currentPrice * (1 - parameters.StopLossPercent / 100m) :
                currentPrice * (1 + parameters.StopLossPercent / 100m);
            
            // Round to tick size
            takeProfitPrice = _strategy.Instrument.MasterInstrument.Round2TickSize(takeProfitPrice);
            stopLossPrice = _strategy.Instrument.MasterInstrument.Round2TickSize(stopLossPrice);
            
            // Place Take Profit order
            var tpOrder = _strategy.SubmitOrder(new OrderRequest
            {
                Type = OrderType.Limit,
                Side = isLong ? OrderSide.Sell : OrderSide.Buy,
                Quantity = marketOrder.Quantity,
                Price = takeProfitPrice,
                Comment = $"TP-{marketOrder.Comment.Split('-').Last()}",
                TimeInForce = TimeInForce.GTC
            });
            
            // Place Stop Loss order
            var slOrder = _strategy.SubmitOrder(new OrderRequest
            {
                Type = OrderType.StopMarket,
                Side = isLong ? OrderSide.Sell : OrderSide.Buy,
                Quantity = marketOrder.Quantity,
                StopPrice = stopLossPrice,
                Comment = $"SL-{marketOrder.Comment.Split('-').Last()}",
                TimeInForce = TimeInForce.GTC
            });
            
            // Track protection orders
            if (tpOrder != null)
            {
                lock (_orderLock)
                {
                    _activeOrders[tpOrder.Id] = new ManagedOrder
                    {
                        Order = tpOrder,
                        OrderType = ManagedOrderType.TakeProfit,
                        ParentOrderId = marketOrder.Id,
                        CreatedAt = DateTime.UtcNow
                    };
                }
            }
            
            if (slOrder != null)
            {
                lock (_orderLock)
                {
                    _activeOrders[slOrder.Id] = new ManagedOrder
                    {
                        Order = slOrder,
                        OrderType = ManagedOrderType.StopLoss,
                        ParentOrderId = marketOrder.Id,
                        CreatedAt = DateTime.UtcNow
                    };
                }
            }
            
            _logger.LogInfo($"🛡️ Protection orders placed: TP @ {takeProfitPrice}, SL @ {stopLossPrice}");
            
            return new ProtectionOrderResult
            {
                Success = true,
                TakeProfitOrder = tpOrder,
                StopLossOrder = slOrder
            };
        }
        catch (Exception ex)
        {
            _logger.LogError($"❌ Protection order placement failed: {ex.Message}");
            return new ProtectionOrderResult { Success = false, ErrorMessage = ex.Message };
        }
    }
}

public class TradingParameters
{
    public int MaxPositions { get; set; } = 1;
    public decimal PositionSizeUSDT { get; set; } = 1000m;
    public decimal TakeProfitPercent { get; set; } = 0.5m;
    public decimal StopLossPercent { get; set; } = 0.35m;
    public decimal MaxDailyLoss { get; set; } = 5.0m;
    public int MaxDailyTrades { get; set; } = 50;
    public decimal MinConfidenceForTrading { get; set; } = 0.85m;
}

public class ManagedOrder
{
    public Order Order { get; set; }
    public CoordinatedSignal Signal { get; set; }
    public ManagedOrderType OrderType { get; set; }
    public string ParentOrderId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? FilledAt { get; set; }
    public DateTime? CancelledAt { get; set; }
}

public enum ManagedOrderType
{
    Market,
    TakeProfit,
    StopLoss
}
```

#### **Task 6.2: Implement Position Tracking System**
**Estimated Time:** 3 hours  
**Assignee:** Position Management Developer  

**Position Tracker Implementation:**
```csharp
public class PositionTracker
{
    private readonly SimpleLogger _logger;
    private readonly object _positionLock = new object();
    private readonly Dictionary<string, Position> _positions;
    private readonly CircularBuffer<PositionEvent> _positionHistory;
    
    public PositionTracker(SimpleLogger logger)
    {
        _logger = logger;
        _positions = new Dictionary<string, Position>();
        _positionHistory = new CircularBuffer<PositionEvent>(1000);
    }
    
    public void OnOrderFilled(Order order, Fill fill)
    {
        lock (_positionLock)
        {
            var positionKey = GetPositionKey(order);
            
            if (!_positions.ContainsKey(positionKey))
            {
                // New position
                var position = new Position
                {
                    Id = Guid.NewGuid().ToString(),
                    Symbol = order.Instrument.FullName,
                    Side = order.Side,
                    Quantity = fill.Quantity,
                    AveragePrice = fill.Price,
                    OpenTime = fill.Time,
                    Status = PositionStatus.Open,
                    RelatedOrders = new List<string> { order.Id }
                };
                
                _positions[positionKey] = position;
                
                var openEvent = new PositionEvent
                {
                    Type = PositionEventType.Opened,
                    Position = position,
                    Timestamp = fill.Time,
                    Details = $"Position opened: {order.Side} {fill.Quantity} @ {fill.Price}"
                };
                
                _positionHistory.Add(openEvent);
                _logger.LogInfo($"📈 Position opened: {position.Id} - {order.Side} {fill.Quantity} @ {fill.Price}");
            }
            else
            {
                // Position modification (partial fill, etc.)
                var position = _positions[positionKey];
                position.Quantity += fill.Quantity;
                position.AveragePrice = CalculateAveragePrice(position, fill);
                position.RelatedOrders.Add(order.Id);
                
                _logger.LogInfo($"📊 Position updated: {position.Id} - New quantity: {position.Quantity}");
            }
        }
    }
    
    public void OnPositionClosed(string positionId, decimal closePrice, DateTime closeTime, string reason)
    {
        lock (_positionLock)
        {
            var position = _positions.Values.FirstOrDefault(p => p.Id == positionId);
            if (position != null)
            {
                position.ClosePrice = closePrice;
                position.CloseTime = closeTime;
                position.Status = PositionStatus.Closed;
                position.PnL = CalculatePnL(position);
                
                var closeEvent = new PositionEvent
                {
                    Type = PositionEventType.Closed,
                    Position = position,
                    Timestamp = closeTime,
                    Details = $"Position closed: {reason} - PnL: {position.PnL:C}"
                };
                
                _positionHistory.Add(closeEvent);
                _logger.LogInfo($"📉 Position closed: {position.Id} - {reason} - PnL: {position.PnL:C}");
                
                // Remove from active positions
                var key = _positions.FirstOrDefault(kvp => kvp.Value.Id == positionId).Key;
                if (key != null)
                {
                    _positions.Remove(key);
                }
            }
        }
    }
    
    public List<Position> GetActivePositions()
    {
        lock (_positionLock)
        {
            return _positions.Values.Where(p => p.Status == PositionStatus.Open).ToList();
        }
    }
    
    public PositionSummary GetPositionSummary()
    {
        lock (_positionLock)
        {
            var activePositions = GetActivePositions();
            var recentHistory = _positionHistory.ToArray().TakeLast(100).ToList();
            
            return new PositionSummary
            {
                ActivePositionCount = activePositions.Count,
                TotalExposure = activePositions.Sum(p => p.Quantity * p.AveragePrice),
                DailyPnL = recentHistory.Where(e => e.Timestamp.Date == DateTime.Today && e.Type == PositionEventType.Closed)
                                      .Sum(e => e.Position.PnL),
                DailyTradeCount = recentHistory.Count(e => e.Timestamp.Date == DateTime.Today && e.Type == PositionEventType.Opened),
                LastUpdate = DateTime.UtcNow
            };
        }
    }
    
    private decimal CalculatePnL(Position position)
    {
        if (!position.ClosePrice.HasValue) return 0;
        
        var priceDiff = position.Side == OrderSide.Buy ? 
            position.ClosePrice.Value - position.AveragePrice :
            position.AveragePrice - position.ClosePrice.Value;
        
        return priceDiff * position.Quantity;
    }
    
    private string GetPositionKey(Order order)
    {
        return $"{order.Instrument.FullName}_{order.Side}";
    }
}

public class Position
{
    public string Id { get; set; }
    public string Symbol { get; set; }
    public OrderSide Side { get; set; }
    public decimal Quantity { get; set; }
    public decimal AveragePrice { get; set; }
    public decimal? ClosePrice { get; set; }
    public DateTime OpenTime { get; set; }
    public DateTime? CloseTime { get; set; }
    public PositionStatus Status { get; set; }
    public decimal PnL { get; set; }
    public List<string> RelatedOrders { get; set; } = new List<string>();
}

public enum PositionStatus
{
    Open,
    Closed,
    Cancelled
}

public class PositionEvent
{
    public PositionEventType Type { get; set; }
    public Position Position { get; set; }
    public DateTime Timestamp { get; set; }
    public string Details { get; set; }
}

public enum PositionEventType
{
    Opened,
    Modified,
    Closed
}

public class PositionSummary
{
    public int ActivePositionCount { get; set; }
    public decimal TotalExposure { get; set; }
    public decimal DailyPnL { get; set; }
    public int DailyTradeCount { get; set; }
    public DateTime LastUpdate { get; set; }
}
```

### **Day 2: Risk Management System**

#### **Task 6.3: Implement Comprehensive Risk Management**
**Estimated Time:** 4 hours  
**Assignee:** Risk Management Developer  

**Risk Manager Implementation:**
```csharp
public class RiskManager
{
    private readonly SimpleLogger _logger;
    private readonly CircularBuffer<RiskEvent> _riskEvents;
    private readonly Dictionary<string, decimal> _dailyLosses;
    private readonly Dictionary<string, int> _dailyTradeCounts;
    
    public RiskManager(SimpleLogger logger)
    {
        _logger = logger;
        _riskEvents = new CircularBuffer<RiskEvent>(1000);
        _dailyLosses = new Dictionary<string, decimal>();
        _dailyTradeCounts = new Dictionary<string, int>();
    }
    
    public RiskValidationResult ValidateSignal(CoordinatedSignal signal, TradingParameters parameters, List<Position> activePositions)
    {
        var validations = new List<IRiskValidation>
        {
            new ConfidenceThresholdValidation(parameters.MinConfidenceForTrading),
            new MaxPositionsValidation(parameters.MaxPositions),
            new DailyLossLimitValidation(parameters.MaxDailyLoss),
            new DailyTradeCountValidation(parameters.MaxDailyTrades),
            new MarketHoursValidation(),
            new VolatilityValidation()
        };
        
        foreach (var validation in validations)
        {
            var result = validation.Validate(signal, parameters, activePositions, GetDailyMetrics());
            if (!result.Passed)
            {
                RecordRiskEvent(RiskEventType.SignalRejected, result.Reason);
                return RiskValidationResult.Rejected(result.Reason);
            }
        }
        
        RecordRiskEvent(RiskEventType.SignalApproved, "All risk checks passed");
        return RiskValidationResult.Approved();
    }
    
    public decimal CalculatePositionSize(CoordinatedSignal signal, TradingParameters parameters)
    {
        var baseSize = parameters.PositionSizeUSDT;
        
        // Adjust size based on signal confidence
        var confidenceMultiplier = Math.Min(1.2m, signal.Confidence / 0.85m);
        
        // Adjust size based on recent performance
        var performanceMultiplier = CalculatePerformanceMultiplier();
        
        // Adjust size based on market volatility
        var volatilityMultiplier = CalculateVolatilityMultiplier();
        
        var adjustedSize = baseSize * confidenceMultiplier * performanceMultiplier * volatilityMultiplier;
        
        // Ensure size is within bounds
        var minSize = baseSize * 0.5m;
        var maxSize = baseSize * 1.5m;
        
        adjustedSize = Math.Max(minSize, Math.Min(maxSize, adjustedSize));
        
        _logger.LogInfo($"💰 Position size calculated: {adjustedSize:C} (Base: {baseSize:C}, " +
                       $"Confidence: {confidenceMultiplier:F2}x, Performance: {performanceMultiplier:F2}x, " +
                       $"Volatility: {volatilityMultiplier:F2}x)");
        
        return adjustedSize;
    }
    
    private decimal CalculatePerformanceMultiplier()
    {
        var recentEvents = _riskEvents.ToArray().TakeLast(50)
                                    .Where(e => e.Type == RiskEventType.TradeCompleted)
                                    .ToList();
        
        if (recentEvents.Count < 10) return 1.0m;
        
        var winRate = recentEvents.Count(e => e.Details.Contains("Profit")) / (decimal)recentEvents.Count;
        
        // Performance multiplier ranges from 0.7 to 1.3
        return 0.7m + (winRate * 0.6m);
    }
    
    private decimal CalculateVolatilityMultiplier()
    {
        // Placeholder for volatility-based sizing
        // In real implementation, this would analyze recent price volatility
        return 1.0m;
    }
    
    private DailyMetrics GetDailyMetrics()
    {
        var today = DateTime.Today.ToString("yyyy-MM-dd");
        
        return new DailyMetrics
        {
            DailyLoss = _dailyLosses.GetValueOrDefault(today, 0),
            DailyTradeCount = _dailyTradeCounts.GetValueOrDefault(today, 0),
            Date = DateTime.Today
        };
    }
    
    private void RecordRiskEvent(RiskEventType type, string details)
    {
        var riskEvent = new RiskEvent
        {
            Type = type,
            Details = details,
            Timestamp = DateTime.UtcNow
        };
        
        _riskEvents.Add(riskEvent);
        
        if (type == RiskEventType.SignalRejected)
        {
            _logger.LogWarning($"⚠️ Risk check failed: {details}");
        }
    }
}

public interface IRiskValidation
{
    string Name { get; }
    ValidationResult Validate(CoordinatedSignal signal, TradingParameters parameters, List<Position> activePositions, DailyMetrics dailyMetrics);
}

public class ConfidenceThresholdValidation : IRiskValidation
{
    private readonly decimal _minConfidence;
    
    public string Name => "ConfidenceThreshold";
    
    public ConfidenceThresholdValidation(decimal minConfidence)
    {
        _minConfidence = minConfidence;
    }
    
    public ValidationResult Validate(CoordinatedSignal signal, TradingParameters parameters, List<Position> activePositions, DailyMetrics dailyMetrics)
    {
        if (signal.Confidence < _minConfidence)
        {
            return ValidationResult.Failed($"Signal confidence {signal.Confidence:P1} below threshold {_minConfidence:P1}");
        }
        
        return ValidationResult.Passed();
    }
}

public class MaxPositionsValidation : IRiskValidation
{
    public string Name => "MaxPositions";
    
    public ValidationResult Validate(CoordinatedSignal signal, TradingParameters parameters, List<Position> activePositions, DailyMetrics dailyMetrics)
    {
        if (activePositions.Count >= parameters.MaxPositions)
        {
            return ValidationResult.Failed($"Maximum positions reached: {activePositions.Count}/{parameters.MaxPositions}");
        }
        
        return ValidationResult.Passed();
    }
}

public class RiskValidationResult
{
    public bool Approved { get; set; }
    public string Reason { get; set; }
    
    public static RiskValidationResult Approved() => new RiskValidationResult { Approved = true };
    public static RiskValidationResult Rejected(string reason) => new RiskValidationResult { Approved = false, Reason = reason };
}

public class ValidationResult
{
    public bool Passed { get; set; }
    public string Reason { get; set; }
    
    public static ValidationResult Passed() => new ValidationResult { Passed = true };
    public static ValidationResult Failed(string reason) => new ValidationResult { Passed = false, Reason = reason };
}

public class RiskEvent
{
    public RiskEventType Type { get; set; }
    public string Details { get; set; }
    public DateTime Timestamp { get; set; }
}

public enum RiskEventType
{
    SignalApproved,
    SignalRejected,
    TradeCompleted,
    RiskLimitHit
}

public class DailyMetrics
{
    public decimal DailyLoss { get; set; }
    public int DailyTradeCount { get; set; }
    public DateTime Date { get; set; }
}
```
