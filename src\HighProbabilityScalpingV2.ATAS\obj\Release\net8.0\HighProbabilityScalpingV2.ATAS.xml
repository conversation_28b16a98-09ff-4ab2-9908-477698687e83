<?xml version="1.0"?>
<doc>
    <assembly>
        <name>HighProbabilityScalpingV2.ATAS</name>
    </assembly>
    <members>
        <member name="T:HighProbabilityScalpingV2.ATAS.Components.Indicators.BollingerBands">
            <summary>
            Bollinger Bands indicator implementation
            20-period moving average with 2 standard deviation bands
            Phase 2 indicator with 20% weight in signal generation
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.BollingerBands.#ctor(System.Int32,System.Decimal,HighProbabilityScalpingV2.ATAS.Components.Interfaces.PriceSource)">
            <summary>
            Creates a new Bollinger Bands indicator
            </summary>
            <param name="period">Period for moving average calculation (default: 20)</param>
            <param name="standardDeviations">Number of standard deviations for bands (default: 2.0)</param>
            <param name="priceSource">Price source for calculation (default: Close)</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.BollingerBands.Calculate(HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle,HighProbabilityScalpingV2.ATAS.Components.Interfaces.MarketData)">
            <summary>
            Calculates Bollinger Bands and generates trading signal (IIndicator interface)
            </summary>
            <param name="candle">Current candle data</param>
            <param name="marketData">Additional market data (unused in Phase 2)</param>
            <returns>Trading signal with confidence level</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.BollingerBands.CalculateBands">
            <summary>
            Calculates the Bollinger Bands values
            </summary>
            <returns>Tuple of (middle, upper, lower, bandwidth)</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.BollingerBands.GenerateSignal(System.Decimal,System.Decimal,System.Decimal,System.Decimal,System.Decimal,HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle)">
            <summary>
            Generates trading signal based on Bollinger Bands
            </summary>
            <param name="price">Current price</param>
            <param name="middle">Middle band (MA)</param>
            <param name="upper">Upper band</param>
            <param name="lower">Lower band</param>
            <param name="bandwidth">Current bandwidth</param>
            <param name="candle">Current candle for context</param>
            <returns>Trading signal</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.BollingerBands.CalculateOversoldConfidence(System.Decimal,System.Decimal)">
            <summary>
            Calculates confidence for oversold (buy) signals
            </summary>
            <param name="bandPosition">Position within bands (0-1)</param>
            <param name="bandwidth">Current bandwidth</param>
            <returns>Confidence level (0.0 to 1.0)</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.BollingerBands.CalculateOverboughtConfidence(System.Decimal,System.Decimal)">
            <summary>
            Calculates confidence for overbought (sell) signals
            </summary>
            <param name="bandPosition">Position within bands (0-1)</param>
            <param name="bandwidth">Current bandwidth</param>
            <returns>Confidence level (0.0 to 1.0)</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.BollingerBands.CalculateMeanReversionConfidence(System.Decimal,System.Boolean)">
            <summary>
            Calculates confidence for mean reversion signals
            </summary>
            <param name="bandPosition">Position within bands (0-1)</param>
            <param name="isBuySignal">Whether this is a buy signal</param>
            <returns>Confidence level (0.0 to 1.0)</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.BollingerBands.GetPrice(HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle,HighProbabilityScalpingV2.ATAS.Components.Interfaces.PriceSource)">
            <summary>
            Gets price value based on specified source
            </summary>
            <param name="candle">Candle data</param>
            <param name="source">Price source</param>
            <returns>Price value</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.BollingerBands.Reset">
            <summary>
            Resets the indicator state and clears all historical data
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.BollingerBands.Name">
            <summary>
            Name of the indicator
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.BollingerBands.Category">
            <summary>
            Category of the indicator
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.BollingerBands.Weight">
            <summary>
            Weight of this indicator in signal aggregation
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.BollingerBands.IsEnabled">
            <summary>
            Whether this indicator is enabled for calculations
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.BollingerBands.IsReady">
            <summary>
            Whether the indicator has enough data to generate reliable signals
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.BollingerBands.LastCalculationTime">
            <summary>
            Duration of the last calculation for performance monitoring
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.BollingerBands.GetStatus">
            <summary>
            Gets current operational status of the indicator
            </summary>
            <returns>Status information for monitoring and debugging</returns>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.BollingerBands.MiddleBand">
            <summary>
            Current middle band value (moving average)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.BollingerBands.UpperBand">
            <summary>
            Current upper band value
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.BollingerBands.LowerBand">
            <summary>
            Current lower band value
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.BollingerBands.Bandwidth">
            <summary>
            Current bandwidth (volatility measure)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.BollingerBands.CalculationCount">
            <summary>
            Number of calculations performed
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.BollingerBands.LastUpdateTime">
            <summary>
            Timestamp of last update
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.BollingerBands.Period">
            <summary>
            Indicator configuration
            </summary>
        </member>
        <member name="T:HighProbabilityScalpingV2.ATAS.Components.Indicators.StochasticOscillator">
            <summary>
            Stochastic Oscillator indicator implementation
            14-period %K with 3-period %D smoothing, 80/20 thresholds
            Phase 2 indicator with 15% weight in signal generation
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.StochasticOscillator.#ctor(System.Int32,System.Int32,System.Decimal,System.Decimal)">
            <summary>
            Creates a new Stochastic Oscillator indicator
            </summary>
            <param name="kPeriod">Period for %K calculation (default: 14)</param>
            <param name="dPeriod">Period for %D smoothing (default: 3)</param>
            <param name="overboughtLevel">Overbought threshold (default: 80)</param>
            <param name="oversoldLevel">Oversold threshold (default: 20)</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.StochasticOscillator.Calculate(HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle,HighProbabilityScalpingV2.ATAS.Components.Interfaces.MarketData)">
            <summary>
            Calculates Stochastic values and generates trading signal (IIndicator interface)
            </summary>
            <param name="candle">Current candle data</param>
            <param name="marketData">Additional market data (unused in Phase 2)</param>
            <returns>Trading signal with confidence level</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.StochasticOscillator.CalculateK">
            <summary>
            Calculates %K value
            </summary>
            <returns>%K value (0-100)</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.StochasticOscillator.CalculateD">
            <summary>
            Calculates %D value (smoothed %K)
            </summary>
            <returns>%D value (0-100)</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.StochasticOscillator.GenerateSignal(System.Decimal,System.Decimal,HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle)">
            <summary>
            Generates trading signal based on Stochastic values
            </summary>
            <param name="k">Current %K value</param>
            <param name="d">Current %D value</param>
            <param name="candle">Current candle for context</param>
            <returns>Trading signal</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.StochasticOscillator.CalculateCrossoverConfidence(System.Decimal,System.Decimal,System.Boolean)">
            <summary>
            Calculates confidence for crossover signals
            </summary>
            <param name="k">Current %K value</param>
            <param name="d">Current %D value</param>
            <param name="isBuySignal">Whether this is a buy signal</param>
            <returns>Confidence level (0.0 to 1.0)</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.StochasticOscillator.CalculateOverboughtConfidence(System.Decimal,System.Decimal)">
            <summary>
            Calculates confidence for overbought (sell) signals
            </summary>
            <param name="k">Current %K value</param>
            <param name="d">Current %D value</param>
            <returns>Confidence level (0.0 to 1.0)</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.StochasticOscillator.CalculateOversoldConfidence(System.Decimal,System.Decimal)">
            <summary>
            Calculates confidence for oversold (buy) signals
            </summary>
            <param name="k">Current %K value</param>
            <param name="d">Current %D value</param>
            <returns>Confidence level (0.0 to 1.0)</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.StochasticOscillator.Reset">
            <summary>
            Resets the indicator state and clears all historical data
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.StochasticOscillator.Name">
            <summary>
            Name of the indicator
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.StochasticOscillator.Category">
            <summary>
            Category of the indicator
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.StochasticOscillator.Weight">
            <summary>
            Weight of this indicator in signal aggregation
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.StochasticOscillator.IsEnabled">
            <summary>
            Whether this indicator is enabled for calculations
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.StochasticOscillator.IsReady">
            <summary>
            Whether the indicator has enough data to generate reliable signals
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.StochasticOscillator.IsKReady">
            <summary>
            Whether %K calculation is ready
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.StochasticOscillator.LastCalculationTime">
            <summary>
            Duration of the last calculation for performance monitoring
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.StochasticOscillator.GetStatus">
            <summary>
            Gets current operational status of the indicator
            </summary>
            <returns>Status information for monitoring and debugging</returns>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.StochasticOscillator.CurrentK">
            <summary>
            Current %K value
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.StochasticOscillator.CurrentD">
            <summary>
            Current %D value
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.StochasticOscillator.CalculationCount">
            <summary>
            Number of calculations performed
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.StochasticOscillator.LastUpdateTime">
            <summary>
            Timestamp of last update
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.StochasticOscillator.KPeriod">
            <summary>
            Indicator configuration
            </summary>
        </member>
        <member name="T:HighProbabilityScalpingV2.ATAS.Components.Indicators.UltraFastRSI">
            <summary>
            Ultra-Fast RSI (Relative Strength Index) indicator implementation
            Optimized for ultra-low timeframe scalping with 3-period calculation
            Phase 2 indicator with 25% weight in signal generation
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.UltraFastRSI.#ctor(System.Int32,System.Decimal,System.Decimal,HighProbabilityScalpingV2.ATAS.Components.Interfaces.PriceSource)">
            <summary>
            Creates a new Ultra-Fast RSI indicator
            </summary>
            <param name="period">Period for RSI calculation (default: 3 for ultra-fast)</param>
            <param name="overboughtLevel">Overbought threshold (default: 80)</param>
            <param name="oversoldLevel">Oversold threshold (default: 20)</param>
            <param name="priceSource">Price source for calculation (default: Close)</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.UltraFastRSI.Calculate(HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle,HighProbabilityScalpingV2.ATAS.Components.Interfaces.MarketData)">
            <summary>
            Calculates RSI value and generates trading signal (IIndicator interface)
            </summary>
            <param name="candle">Current candle data</param>
            <param name="marketData">Additional market data (unused in Phase 2)</param>
            <returns>Trading signal with confidence level</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.UltraFastRSI.CalculateRSI">
            <summary>
            Calculates the RSI value using the standard formula
            </summary>
            <returns>RSI value (0-100)</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.UltraFastRSI.GenerateSignal(System.Decimal,HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle)">
            <summary>
            Generates trading signal based on RSI value
            </summary>
            <param name="rsi">Current RSI value</param>
            <param name="candle">Current candle for context</param>
            <returns>Trading signal</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.UltraFastRSI.CalculateOverboughtConfidence(System.Decimal)">
            <summary>
            Calculates confidence for overbought (sell) signals
            </summary>
            <param name="rsi">Current RSI value</param>
            <returns>Confidence level (0.0 to 1.0)</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.UltraFastRSI.CalculateOversoldConfidence(System.Decimal)">
            <summary>
            Calculates confidence for oversold (buy) signals
            </summary>
            <param name="rsi">Current RSI value</param>
            <returns>Confidence level (0.0 to 1.0)</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.UltraFastRSI.CalculateTrendBoost(System.Boolean)">
            <summary>
            Calculates trend boost for confidence
            </summary>
            <param name="isBuySignal">Whether this is for a buy signal</param>
            <returns>Trend boost value</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.UltraFastRSI.GetPrice(HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle,HighProbabilityScalpingV2.ATAS.Components.Interfaces.PriceSource)">
            <summary>
            Gets price value based on specified source
            </summary>
            <param name="candle">Candle data</param>
            <param name="source">Price source</param>
            <returns>Price value</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.UltraFastRSI.Reset">
            <summary>
            Resets the indicator state and clears all historical data
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.UltraFastRSI.Name">
            <summary>
            Name of the indicator
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.UltraFastRSI.Category">
            <summary>
            Category of the indicator
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.UltraFastRSI.Weight">
            <summary>
            Weight of this indicator in signal aggregation
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.UltraFastRSI.IsEnabled">
            <summary>
            Whether this indicator is enabled for calculations
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.UltraFastRSI.IsReady">
            <summary>
            Whether the indicator has enough data to generate reliable signals
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.UltraFastRSI.LastCalculationTime">
            <summary>
            Duration of the last calculation for performance monitoring
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.UltraFastRSI.GetStatus">
            <summary>
            Gets current operational status of the indicator
            </summary>
            <returns>Status information for monitoring and debugging</returns>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.UltraFastRSI.CurrentValue">
            <summary>
            Current RSI value (latest calculated)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.UltraFastRSI.CalculationCount">
            <summary>
            Number of calculations performed
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.UltraFastRSI.LastUpdateTime">
            <summary>
            Timestamp of last update
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.UltraFastRSI.Period">
            <summary>
            Indicator configuration
            </summary>
        </member>
        <member name="T:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex">
            <summary>
            Volume Flow Index (VFI) indicator implementation
            Optimized for ultra-low timeframe scalping with less than 5ms performance target
            Primary indicator for Phase 1 with 30% weight in signal generation
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.#ctor(System.Int32,System.Decimal,System.Decimal)">
            <summary>
            Creates a new Volume Flow Index indicator
            </summary>
            <param name="period">Period for VFI calculation (default: 14)</param>
            <param name="buyThreshold">Threshold for buy signals (default: 1.3)</param>
            <param name="sellThreshold">Threshold for sell signals (default: 0.7)</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.Calculate(HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle)">
            <summary>
            Calculates VFI value and generates trading signal
            </summary>
            <param name="candle">Current candle data</param>
            <returns>Trading signal with confidence level</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.Calculate(HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle,HighProbabilityScalpingV2.ATAS.Components.Interfaces.MarketData)">
            <summary>
            Calculates VFI value and generates trading signal (IIndicator interface)
            </summary>
            <param name="candle">Current candle data</param>
            <param name="marketData">Additional market data (unused in Phase 1)</param>
            <returns>Trading signal with confidence level</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.CalculateVFI">
            <summary>
            Calculates the Volume Flow Index value
            </summary>
            <returns>VFI value</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.GenerateSignal(System.Decimal,HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle)">
            <summary>
            Generates trading signal based on VFI value
            </summary>
            <param name="vfi">Current VFI value</param>
            <param name="candle">Current candle for context</param>
            <returns>Trading signal</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.CalculateBuyConfidence(System.Decimal)">
            <summary>
            Calculates confidence for buy signals
            </summary>
            <param name="vfi">Current VFI value</param>
            <returns>Confidence level (0.0 to 1.0)</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.CalculateSellConfidence(System.Decimal)">
            <summary>
            Calculates confidence for sell signals
            </summary>
            <param name="vfi">Current VFI value</param>
            <returns>Confidence level (0.0 to 1.0)</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.CalculateTrendBoost(System.Boolean)">
            <summary>
            Calculates trend boost for confidence
            </summary>
            <param name="isBuySignal">Whether this is for a buy signal</param>
            <returns>Trend boost value</returns>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.IsReady">
            <summary>
            Whether the indicator has enough data to generate signals
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.CurrentValue">
            <summary>
            Current VFI value (latest calculated)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.LastCalculationTime">
            <summary>
            Performance metrics
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.Period">
            <summary>
            Indicator configuration
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.Name">
            <summary>
            Name of the indicator
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.Category">
            <summary>
            Category of the indicator
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.Weight">
            <summary>
            Weight of this indicator in signal aggregation
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.IsEnabled">
            <summary>
            Whether this indicator is enabled for calculations
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.GetStatusString">
            <summary>
            Gets indicator status for debugging (legacy method)
            </summary>
            <returns>Status string</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.GetStatus">
            <summary>
            Gets current operational status of the indicator (IIndicator interface)
            </summary>
            <returns>Status information for monitoring and debugging</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.Reset">
            <summary>
            Resets the indicator state
            </summary>
        </member>
        <member name="T:HighProbabilityScalpingV2.ATAS.Components.Interfaces.IIndicator">
            <summary>
            Interface for all Phase 1 indicators in the High-Probability Scalping Strategy v2.0
            Provides standardized interface for indicator calculations, status reporting, and performance tracking
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Interfaces.IIndicator.Name">
            <summary>
            Name of the indicator (e.g., "VFI", "RSI", "BollingerBands")
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Interfaces.IIndicator.Category">
            <summary>
            Category of the indicator (e.g., "Volume", "Momentum", "Volatility", "Trend")
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Interfaces.IIndicator.Weight">
            <summary>
            Weight of this indicator in signal aggregation (0.0 to 1.0)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Interfaces.IIndicator.IsEnabled">
            <summary>
            Whether this indicator is enabled for calculations
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Interfaces.IIndicator.IsReady">
            <summary>
            Whether the indicator has enough data to generate reliable signals
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Interfaces.IIndicator.LastCalculationTime">
            <summary>
            Duration of the last calculation for performance monitoring
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Interfaces.IIndicator.Calculate(HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle,HighProbabilityScalpingV2.ATAS.Components.Interfaces.MarketData)">
            <summary>
            Calculates indicator signal based on current market data
            </summary>
            <param name="candle">Current candle data</param>
            <param name="marketData">Additional market data (for future use)</param>
            <returns>Indicator signal with direction, confidence, and reasoning</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Interfaces.IIndicator.Reset">
            <summary>
            Resets the indicator state and clears all historical data
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Interfaces.IIndicator.GetStatus">
            <summary>
            Gets current operational status of the indicator
            </summary>
            <returns>Status information for monitoring and debugging</returns>
        </member>
        <member name="T:HighProbabilityScalpingV2.ATAS.Components.Interfaces.IndicatorStatus">
            <summary>
            Represents the operational status of an indicator
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Interfaces.IndicatorStatus.IsOperational">
            <summary>
            Whether the indicator is operational and ready to generate signals
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Interfaces.IndicatorStatus.StatusMessage">
            <summary>
            Human-readable status message
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Interfaces.IndicatorStatus.DataPointsRequired">
            <summary>
            Number of data points required for the indicator to be ready
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Interfaces.IndicatorStatus.DataPointsAvailable">
            <summary>
            Number of data points currently available
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Interfaces.IndicatorStatus.LastUpdate">
            <summary>
            Timestamp of the last status update
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Interfaces.IndicatorStatus.Metadata">
            <summary>
            Additional metadata for debugging
            </summary>
        </member>
        <member name="T:HighProbabilityScalpingV2.ATAS.Components.Interfaces.MarketData">
            <summary>
            Placeholder for future market data integration
            Currently used as a placeholder for Phase 3 order flow data
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Interfaces.MarketData.Bid">
            <summary>
            Current bid price (placeholder)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Interfaces.MarketData.Ask">
            <summary>
            Current ask price (placeholder)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Interfaces.MarketData.Spread">
            <summary>
            Current spread (placeholder)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Interfaces.MarketData.BidVolume">
            <summary>
            Volume at bid (placeholder for order flow)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Interfaces.MarketData.AskVolume">
            <summary>
            Volume at ask (placeholder for order flow)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Interfaces.MarketData.AdditionalData">
            <summary>
            Additional market data for future phases
            </summary>
        </member>
        <member name="T:HighProbabilityScalpingV2.ATAS.Components.Interfaces.PriceSource">
            <summary>
            Enumeration for price source selection in indicators
            </summary>
        </member>
        <member name="F:HighProbabilityScalpingV2.ATAS.Components.Interfaces.PriceSource.Open">
            <summary>
            Opening price of the candle
            </summary>
        </member>
        <member name="F:HighProbabilityScalpingV2.ATAS.Components.Interfaces.PriceSource.High">
            <summary>
            Highest price of the candle
            </summary>
        </member>
        <member name="F:HighProbabilityScalpingV2.ATAS.Components.Interfaces.PriceSource.Low">
            <summary>
            Lowest price of the candle
            </summary>
        </member>
        <member name="F:HighProbabilityScalpingV2.ATAS.Components.Interfaces.PriceSource.Close">
            <summary>
            Closing price of the candle
            </summary>
        </member>
        <member name="F:HighProbabilityScalpingV2.ATAS.Components.Interfaces.PriceSource.Typical">
            <summary>
            Typical price (High + Low + Close) / 3
            </summary>
        </member>
        <member name="F:HighProbabilityScalpingV2.ATAS.Components.Interfaces.PriceSource.Median">
            <summary>
            Median price (High + Low) / 2
            </summary>
        </member>
        <member name="F:HighProbabilityScalpingV2.ATAS.Components.Interfaces.PriceSource.WeightedClose">
            <summary>
            Weighted close (High + Low + Close + Close) / 4
            </summary>
        </member>
        <member name="T:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger">
            <summary>
            Simple, high-performance logging system for ATAS strategies
            Avoids complex dependencies and provides thread-safe file logging
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.#ctor(System.String,System.Boolean)">
            <summary>
            Creates a new simple logger instance
            </summary>
            <param name="strategyName">Name of the strategy for log identification</param>
            <param name="enableDebug">Whether to enable debug-level logging</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.LogInfo(System.String)">
            <summary>
            Logs an informational message
            </summary>
            <param name="message">Message to log</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.LogWarning(System.String)">
            <summary>
            Logs a warning message
            </summary>
            <param name="message">Warning message to log</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.LogError(System.String)">
            <summary>
            Logs an error message
            </summary>
            <param name="message">Error message to log</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.LogError(System.String,System.Exception)">
            <summary>
            Logs an error with exception details
            </summary>
            <param name="message">Error message</param>
            <param name="exception">Exception to log</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.LogDebug(System.String)">
            <summary>
            Logs a debug message (only if debug logging is enabled)
            </summary>
            <param name="message">Debug message to log</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.LogSignal(System.String)">
            <summary>
            Logs a signal-related message with special formatting
            </summary>
            <param name="message">Signal message to log</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.LogPerformance(System.String)">
            <summary>
            Logs a performance-related message
            </summary>
            <param name="message">Performance message to log</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.LogTrade(System.String)">
            <summary>
            Logs a trade-related message
            </summary>
            <param name="message">Trade message to log</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.WriteLog(System.String,System.String)">
            <summary>
            Core logging method that writes to file
            </summary>
            <param name="level">Log level</param>
            <param name="message">Message to log</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.LogSeparator(System.String)">
            <summary>
            Logs a separator line for visual organization
            </summary>
            <param name="title">Optional title for the separator</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.LogStartup(System.String,System.String)">
            <summary>
            Logs strategy startup information
            </summary>
            <param name="version">Strategy version</param>
            <param name="phase">Current phase</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.LogShutdown">
            <summary>
            Logs strategy shutdown information
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.LogFilePath">
            <summary>
            Gets the current log file path
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.IsDebugEnabled">
            <summary>
            Whether debug logging is enabled
            </summary>
        </member>
        <member name="T:HighProbabilityScalpingV2.ATAS.Components.Models.SignalDirection">
            <summary>
            Represents the direction of a trading signal
            </summary>
        </member>
        <member name="F:HighProbabilityScalpingV2.ATAS.Components.Models.SignalDirection.Neutral">
            <summary>
            No clear direction - neutral signal
            </summary>
        </member>
        <member name="F:HighProbabilityScalpingV2.ATAS.Components.Models.SignalDirection.Buy">
            <summary>
            Bullish signal - suggests buying
            </summary>
        </member>
        <member name="F:HighProbabilityScalpingV2.ATAS.Components.Models.SignalDirection.Sell">
            <summary>
            Bearish signal - suggests selling
            </summary>
        </member>
        <member name="T:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal">
            <summary>
            Represents a trading signal generated by an indicator
            Contains direction, confidence level, and reasoning
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.Direction">
            <summary>
            Direction of the signal (Buy, Sell, or Neutral)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.Confidence">
            <summary>
            Confidence level of the signal (0.0 to 1.0)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.Timestamp">
            <summary>
            Timestamp when the signal was generated
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.Reason">
            <summary>
            Human-readable reason for the signal
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.IndicatorName">
            <summary>
            Name of the indicator that generated this signal
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.RawValue">
            <summary>
            Raw indicator value that generated the signal
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.Metadata">
            <summary>
            Additional metadata for debugging and analysis
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.#ctor(HighProbabilityScalpingV2.ATAS.Components.Models.SignalDirection,System.Decimal,System.String,System.String,System.Decimal)">
            <summary>
            Creates a new indicator signal
            </summary>
            <param name="direction">Signal direction</param>
            <param name="confidence">Confidence level (0.0 to 1.0)</param>
            <param name="reason">Reason for the signal</param>
            <param name="indicatorName">Name of the indicator</param>
            <param name="rawValue">Raw indicator value</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.Neutral(System.String,System.String)">
            <summary>
            Creates a neutral signal with specified reason
            </summary>
            <param name="reason">Reason for neutral signal</param>
            <param name="indicatorName">Name of the indicator</param>
            <returns>Neutral signal</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.Buy(System.Decimal,System.String,System.String,System.Decimal)">
            <summary>
            Creates a buy signal with specified confidence
            </summary>
            <param name="confidence">Confidence level</param>
            <param name="reason">Reason for buy signal</param>
            <param name="indicatorName">Name of the indicator</param>
            <param name="rawValue">Raw indicator value</param>
            <returns>Buy signal</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.Sell(System.Decimal,System.String,System.String,System.Decimal)">
            <summary>
            Creates a sell signal with specified confidence
            </summary>
            <param name="confidence">Confidence level</param>
            <param name="reason">Reason for sell signal</param>
            <param name="indicatorName">Name of the indicator</param>
            <param name="rawValue">Raw indicator value</param>
            <returns>Sell signal</returns>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.ConfidencePercent">
            <summary>
            Gets confidence as percentage (0-100)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.IsTradeable">
            <summary>
            Whether this is a tradeable signal (not neutral)
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.MeetsThreshold(System.Decimal)">
            <summary>
            Whether this signal meets a minimum confidence threshold
            </summary>
            <param name="threshold">Minimum confidence (0.0 to 1.0)</param>
            <returns>True if signal meets threshold</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.ToString">
            <summary>
            String representation of the signal
            </summary>
            <returns>Formatted signal description</returns>
        </member>
        <member name="T:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle">
            <summary>
            Represents a candle for indicator calculations
            Simplified version of ATAS IndicatorCandle for internal use
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.Open">
            <summary>
            Opening price
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.High">
            <summary>
            Highest price
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.Low">
            <summary>
            Lowest price
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.Close">
            <summary>
            Closing price
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.Volume">
            <summary>
            Volume traded
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.Time">
            <summary>
            Timestamp of the candle
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.TypicalPrice">
            <summary>
            Typical price (HLC/3)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.MedianPrice">
            <summary>
            Median price (HL/2)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.WeightedClose">
            <summary>
            Weighted close price (HLCC/4)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.Range">
            <summary>
            Price range (High - Low)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.IsBullish">
            <summary>
            Whether this is a bullish candle
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.IsBearish">
            <summary>
            Whether this is a bearish candle
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.BodySize">
            <summary>
            Body size of the candle
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.#ctor">
            <summary>
            Creates a new indicator candle
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.#ctor(System.Decimal,System.Decimal,System.Decimal,System.Decimal,System.Decimal,System.DateTime)">
            <summary>
            Creates a new indicator candle with specified values
            </summary>
        </member>
        <member name="T:HighProbabilityScalpingV2.ATAS.Components.Performance.PerformanceMonitor">
            <summary>
            High-performance monitoring system for tracking operation timings
            Designed to meet less than 20ms processing requirements for Phase 1
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Performance.PerformanceMonitor.#ctor(HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger)">
            <summary>
            Creates a new performance monitor
            </summary>
            <param name="logger">Logger for performance reporting</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Performance.PerformanceMonitor.StartTiming(System.String)">
            <summary>
            Starts timing an operation and returns a disposable scope
            </summary>
            <param name="operation">Name of the operation being timed</param>
            <returns>Disposable timing scope</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Performance.PerformanceMonitor.RecordTiming(System.String,System.TimeSpan)">
            <summary>
            Records a timing measurement for an operation
            </summary>
            <param name="operation">Operation name</param>
            <param name="duration">Duration of the operation</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Performance.PerformanceMonitor.IncrementCounter(System.String)">
            <summary>
            Increments a simple counter
            </summary>
            <param name="counterName">Name of the counter</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Performance.PerformanceMonitor.GetAverageTiming(System.String)">
            <summary>
            Gets average timing for an operation
            </summary>
            <param name="operation">Operation name</param>
            <returns>Average duration, or TimeSpan.Zero if no data</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Performance.PerformanceMonitor.GetMaxTiming(System.String)">
            <summary>
            Gets maximum timing for an operation
            </summary>
            <param name="operation">Operation name</param>
            <returns>Maximum duration, or TimeSpan.Zero if no data</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Performance.PerformanceMonitor.GetCount(System.String)">
            <summary>
            Gets count for an operation or counter
            </summary>
            <param name="name">Operation or counter name</param>
            <returns>Count value</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Performance.PerformanceMonitor.LogPerformanceReport">
            <summary>
            Logs a comprehensive performance report
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Performance.PerformanceMonitor.GetPerformanceStatus(System.String,System.Double)">
            <summary>
            Gets performance status emoji based on timing
            </summary>
            <param name="operation">Operation name</param>
            <param name="averageMs">Average timing in milliseconds</param>
            <returns>Status emoji</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Performance.PerformanceMonitor.LogQuickSummary">
            <summary>
            Logs a quick performance summary
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Performance.PerformanceMonitor.Reset">
            <summary>
            Clears all performance data
            </summary>
        </member>
        <member name="T:HighProbabilityScalpingV2.ATAS.Components.Performance.TimingScope">
            <summary>
            Disposable timing scope for automatic timing measurement
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Performance.TimingScope.#ctor(System.String,HighProbabilityScalpingV2.ATAS.Components.Performance.PerformanceMonitor)">
            <summary>
            Creates a new timing scope
            </summary>
            <param name="operation">Operation being timed</param>
            <param name="monitor">Performance monitor to report to</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Performance.TimingScope.Dispose">
            <summary>
            Completes timing and reports duration
            </summary>
        </member>
        <member name="T:HighProbabilityScalpingV2.ATAS.Components.SignalProcessing.SignalAggregator">
            <summary>
            Aggregates signals from multiple indicators to generate final trading decisions
            Implements weighted voting system with confidence thresholds
            Phase 2 component for coordinating VFI, RSI, Bollinger Bands, and Stochastic signals
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.SignalProcessing.SignalAggregator.#ctor(HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger,System.Decimal,System.Int32)">
            <summary>
            Creates a new signal aggregator
            </summary>
            <param name="logger">Logger for signal processing</param>
            <param name="minimumConfidence">Minimum confidence threshold (0.0 to 1.0)</param>
            <param name="minimumIndicatorAlignment">Minimum number of indicators that must agree</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.SignalProcessing.SignalAggregator.AddIndicator(HighProbabilityScalpingV2.ATAS.Components.Interfaces.IIndicator)">
            <summary>
            Adds an indicator to the aggregation system
            </summary>
            <param name="indicator">Indicator to add</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.SignalProcessing.SignalAggregator.RemoveIndicator(System.String)">
            <summary>
            Removes an indicator from the aggregation system
            </summary>
            <param name="indicatorName">Name of indicator to remove</param>
            <returns>True if indicator was found and removed</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.SignalProcessing.SignalAggregator.AggregateSignals(HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle,HighProbabilityScalpingV2.ATAS.Components.Interfaces.MarketData)">
            <summary>
            Aggregates signals from all indicators to generate final trading signal
            </summary>
            <param name="candle">Current candle data</param>
            <param name="marketData">Additional market data</param>
            <returns>Aggregated signal with combined confidence</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.SignalProcessing.SignalAggregator.PerformSignalAggregation(System.Collections.Generic.List{HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal},System.Collections.Generic.List{HighProbabilityScalpingV2.ATAS.Components.Interfaces.IIndicator})">
            <summary>
            Performs the actual signal aggregation using weighted voting
            </summary>
            <param name="signals">Individual indicator signals</param>
            <param name="indicators">Corresponding indicators</param>
            <returns>Aggregated signal</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.SignalProcessing.SignalAggregator.CalculateWeightedScore(System.Collections.Generic.List{HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal},System.Collections.Generic.List{HighProbabilityScalpingV2.ATAS.Components.Interfaces.IIndicator})">
            <summary>
            Calculates weighted score for signals in a specific direction
            </summary>
            <param name="signals">Signals in the same direction</param>
            <param name="indicators">All indicators</param>
            <returns>Weighted score</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.SignalProcessing.SignalAggregator.GetStatistics">
            <summary>
            Gets aggregation statistics
            </summary>
            <returns>Statistics summary</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.SignalProcessing.SignalAggregator.GetIndicators">
            <summary>
            Gets list of all indicators
            </summary>
            <returns>Read-only list of indicators</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.SignalProcessing.SignalAggregator.GetIndicator(System.String)">
            <summary>
            Gets indicator by name
            </summary>
            <param name="name">Indicator name</param>
            <returns>Indicator or null if not found</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.SignalProcessing.SignalAggregator.UpdateConfiguration(System.Decimal,System.Int32)">
            <summary>
            Updates configuration
            </summary>
            <param name="minimumConfidence">New minimum confidence threshold</param>
            <param name="minimumIndicatorAlignment">New minimum indicator alignment</param>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.SignalProcessing.SignalAggregator.AggregationCount">
            <summary>
            Performance metrics
            </summary>
        </member>
        <member name="T:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1">
            <summary>
            High-performance circular buffer for storing fixed-size collections
            Optimized for indicator calculations with minimal memory allocations
            </summary>
            <typeparam name="T">Type of elements to store</typeparam>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1.#ctor(System.Int32)">
            <summary>
            Initializes a new circular buffer with specified capacity
            </summary>
            <param name="capacity">Maximum number of elements to store</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1.Add(`0)">
            <summary>
            Adds an item to the buffer, overwriting oldest item if at capacity
            </summary>
            <param name="item">Item to add</param>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1.Item(System.Int32)">
            <summary>
            Gets element at specified index (0 = oldest, Count-1 = newest)
            </summary>
            <param name="index">Index of element to retrieve</param>
            <returns>Element at specified index</returns>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1.Latest">
            <summary>
            Gets the most recently added element
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1.Count">
            <summary>
            Number of elements currently in the buffer
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1.Capacity">
            <summary>
            Maximum capacity of the buffer
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1.IsFull">
            <summary>
            Whether the buffer is at full capacity
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1.IsEmpty">
            <summary>
            Whether the buffer is empty
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1.ToArray">
            <summary>
            Converts buffer contents to array (oldest to newest)
            </summary>
            <returns>Array containing all elements in chronological order</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1.Sum">
            <summary>
            Calculates sum of numeric elements (for decimal type)
            </summary>
            <returns>Sum of all elements</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1.Average">
            <summary>
            Calculates average of numeric elements (for decimal type)
            </summary>
            <returns>Average of all elements</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1.Clear">
            <summary>
            Clears all elements from the buffer
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1.GetElements">
            <summary>
            Gets enumerable of all elements (oldest to newest)
            </summary>
            <returns>Enumerable of elements</returns>
        </member>
        <member name="T:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy">
             <summary>
             High-Probability Scalping Strategy v2.0 - Phase 2 Implementation
            
             This is a complete rebuild of the trading strategy focusing on:
             - ATAS-native design without complex dependency injection
             - Multi-indicator system: VFI, Ultra-Fast RSI, Bollinger Bands, Stochastic
             - Less than 20ms performance target per calculation
             - 85% plus confidence signal generation through weighted aggregation
             - Clean, maintainable architecture for incremental expansion
            
             Phase 2 Success Criteria:
             All Phase 1 indicators working plus 3 new indicators
             Signal aggregation with weighted voting system
             Performance less than 20ms per calculation
             85% plus confidence threshold with 3+ indicator alignment
             </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.OnStarted">
            <summary>
            Called when strategy starts
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.Dispose">
            <summary>
            Called when strategy stops
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.OnCalculate(System.Int32,System.Decimal)">
            <summary>
            Main calculation method called for each bar
            </summary>
            <param name="bar">Bar index</param>
            <param name="value">Bar value</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.ProcessIndicatorSignal(System.Int32)">
            <summary>
            Processes aggregated indicator signals for current bar
            </summary>
            <param name="bar">Current bar index</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.CreateMarketDataFromCandle(HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle)">
            <summary>
            Creates MarketData from ATAS candle for indicator processing
            </summary>
            <param name="candle">ATAS candle</param>
            <returns>MarketData for indicators</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.LogSignalMetadata(HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal)">
            <summary>
            Logs detailed signal metadata for analysis
            </summary>
            <param name="signal">Signal with metadata</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.LogIndividualIndicatorStatus(System.Int32)">
            <summary>
            Logs individual indicator status for debugging
            </summary>
            <param name="bar">Current bar</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.ProcessQualifiedSignal(HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal,System.Int32)">
            <summary>
            Processes qualified signals that meet confidence threshold
            </summary>
            <param name="signal">Qualified signal</param>
            <param name="bar">Current bar</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.GetIndicatorCandle(System.Int32)">
            <summary>
            Converts ATAS candle to internal candle format
            </summary>
            <param name="bar">Bar index</param>
            <returns>Internal candle representation</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.InitializeComponents">
            <summary>
            Initializes all strategy components
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.ValidateConfiguration">
            <summary>
            Validates strategy configuration
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.LogStartupInfo">
            <summary>
            Logs strategy startup information
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.LogShutdownInfo">
            <summary>
            Logs strategy shutdown information
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.CurrentVFIValue">
            <summary>
            Current VFI value for external monitoring
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.CurrentRSIValue">
            <summary>
            Current RSI value for external monitoring
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.CurrentBBMiddle">
            <summary>
            Current Bollinger Bands middle value for external monitoring
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.CurrentStochK">
            <summary>
            Current Stochastic %K value for external monitoring
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.AreAllIndicatorsReady">
            <summary>
            Whether all Phase 2 indicators are ready
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.ReadyIndicatorCount">
            <summary>
            Number of ready indicators
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.BarsProcessed">
            <summary>
            Total number of bars processed
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.SignalsGenerated">
            <summary>
            Total aggregated signals generated
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.Phase2SignalsGenerated">
            <summary>
            Phase 2 signals generated
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.QualifiedSignals">
            <summary>
            Qualified signals (meeting confidence threshold)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.SignalQualificationRate">
            <summary>
            Signal qualification rate as percentage
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.AverageAggregationTime">
            <summary>
            Average aggregation time in milliseconds
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.StrategyStatus">
            <summary>
            Strategy status for debugging
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.DetailedIndicatorStatus">
            <summary>
            Detailed indicator status
            </summary>
        </member>
    </members>
</doc>
