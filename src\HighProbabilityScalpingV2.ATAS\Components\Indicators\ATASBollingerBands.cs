using ATAS.Indicators;
using ATAS.Indicators.Drawing;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace HighProbabilityScalpingV2.ATAS.Components.Indicators
{
    /// <summary>
    /// ATAS-Compatible Bollinger Bands Indicator
    /// Wraps our custom BB logic in ATAS indicator pattern
    /// </summary>
    [DisplayName("Bollinger Bands")]
    public class ATASBollingerBands : Indicator
    {
        #region Fields
        private readonly ValueDataSeries _upperBandSeries;
        private readonly ValueDataSeries _middleBandSeries;
        private readonly ValueDataSeries _lowerBandSeries;
        private readonly ValueDataSeries _signalSeries;
        
        // Calculation fields
        private int _period = 20;
        private decimal _standardDeviations = 2.0m;
        
        // Data storage
        private readonly CircularBuffer<decimal> _prices;
        
        private decimal _lastUpperBand = 0m;
        private decimal _lastMiddleBand = 0m;
        private decimal _lastLowerBand = 0m;
        #endregion

        #region Properties
        [Display(Name = "Period", Order = 10)]
        public int Period
        {
            get => _period;
            set
            {
                _period = Math.Max(1, value);
                RecalculateValues();
            }
        }

        [Display(Name = "Standard Deviations", Order = 20)]
        public decimal StandardDeviations
        {
            get => _standardDeviations;
            set
            {
                _standardDeviations = Math.Max(0.1m, value);
                RecalculateValues();
            }
        }
        #endregion

        #region Constructor
        public ATASBollingerBands()
            : base(true)
        {
            // Initialize data series for visualization
            _upperBandSeries = (ValueDataSeries)DataSeries[0];
            _upperBandSeries.Name = "Upper Band";
            _upperBandSeries.Color = System.Drawing.Color.Blue;
            _upperBandSeries.VisualType = VisualMode.Line;

            _middleBandSeries = new ValueDataSeries("Middle Band")
            {
                Color = System.Drawing.Color.Gray,
                VisualType = VisualMode.Line
            };
            DataSeries.Add(_middleBandSeries);

            _lowerBandSeries = new ValueDataSeries("Lower Band")
            {
                Color = System.Drawing.Color.Blue,
                VisualType = VisualMode.Line
            };
            DataSeries.Add(_lowerBandSeries);

            _signalSeries = new ValueDataSeries("Signal")
            {
                Color = System.Drawing.Color.Green,
                VisualType = VisualMode.Histogram
            };
            DataSeries.Add(_signalSeries);

            // Initialize circular buffer
            _prices = new CircularBuffer<decimal>(_period);
        }
        #endregion

        #region ATAS Indicator Implementation
        protected override void OnCalculate(int bar, decimal value)
        {
            // Use the provided value (typically close price)
            var currentPrice = value;
            _prices.Add(currentPrice);

            // Calculate Bollinger Bands if we have enough data
            if (_prices.Count >= _period)
            {
                // Calculate moving average (middle band)
                var sum = 0m;
                for (int i = 0; i < _period; i++)
                {
                    sum += _prices[_prices.Count - 1 - i];
                }
                _lastMiddleBand = sum / _period;

                // Calculate standard deviation
                var variance = 0m;
                for (int i = 0; i < _period; i++)
                {
                    var price = _prices[_prices.Count - 1 - i];
                    var diff = price - _lastMiddleBand;
                    variance += diff * diff;
                }
                var standardDeviation = (decimal)Math.Sqrt((double)(variance / _period));

                // Calculate upper and lower bands
                _lastUpperBand = _lastMiddleBand + (_standardDeviations * standardDeviation);
                _lastLowerBand = _lastMiddleBand - (_standardDeviations * standardDeviation);

                // Set values for visualization
                _upperBandSeries[bar] = _lastUpperBand;
                _middleBandSeries[bar] = _lastMiddleBand;
                _lowerBandSeries[bar] = _lastLowerBand;

                // Generate signal based on price position relative to bands
                var signal = 0m;
                if (currentPrice >= _lastUpperBand)
                    signal = -1m; // Sell signal (price at upper band)
                else if (currentPrice <= _lastLowerBand)
                    signal = 1m;  // Buy signal (price at lower band)
                else if (currentPrice > _lastMiddleBand)
                    signal = 0.5m; // Weak buy signal (above middle)
                else if (currentPrice < _lastMiddleBand)
                    signal = -0.5m; // Weak sell signal (below middle)

                _signalSeries[bar] = signal;
            }
        }

        /// <summary>
        /// Gets the upper band value for a specific bar
        /// </summary>
        public decimal GetUpperBand(int bar)
        {
            return _upperBandSeries[bar];
        }

        /// <summary>
        /// Gets the middle band value for a specific bar
        /// </summary>
        public decimal GetMiddleBand(int bar)
        {
            return _middleBandSeries[bar];
        }

        /// <summary>
        /// Gets the lower band value for a specific bar
        /// </summary>
        public decimal GetLowerBand(int bar)
        {
            return _lowerBandSeries[bar];
        }

        /// <summary>
        /// Gets the current band values
        /// </summary>
        public (decimal Upper, decimal Middle, decimal Lower) CurrentBands => 
            (_lastUpperBand, _lastMiddleBand, _lastLowerBand);

        /// <summary>
        /// Checks if indicator is ready (has enough data)
        /// </summary>
        public bool IsReady => _prices.Count >= _period;
        #endregion
    }
}
