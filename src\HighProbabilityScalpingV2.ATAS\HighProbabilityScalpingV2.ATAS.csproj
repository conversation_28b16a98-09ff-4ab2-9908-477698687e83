<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <OutputType>Library</OutputType>
    <AssemblyTitle>High-Probability Scalping Strategy v2.0</AssemblyTitle>
    <AssemblyDescription>Rebuilt High-Probability Scalping Strategy for ATAS Platform - Phase 1</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DefineConstants>TRACE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <!-- ATAS Deployment Configuration -->
  <PropertyGroup>
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
    <PublishSingleFile>false</PublishSingleFile>
    <SelfContained>false</SelfContained>
    <ATASDeploymentPath>$(USERPROFILE)\Documents\ATAS\Strategies</ATASDeploymentPath>
  </PropertyGroup>

  <!-- Minimal dependencies - no complex DI framework -->
  <ItemGroup>
    <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
  </ItemGroup>

  <!-- ATAS Platform References -->
  <ItemGroup>
    <!-- Use local ATAS assemblies -->
    <Reference Include="ATAS.Strategies">
      <HintPath>../SmartVolumeStrategy.ATAS/lib/ATAS.Strategies.dll</HintPath>
      <Private>false</Private>
    </Reference>
    <Reference Include="ATAS.Indicators">
      <HintPath>../SmartVolumeStrategy.ATAS/lib/ATAS.Indicators.dll</HintPath>
      <Private>false</Private>
    </Reference>
    <Reference Include="ATAS.DataFeedsCore">
      <HintPath>../SmartVolumeStrategy.ATAS/lib/ATAS.DataFeedsCore.dll</HintPath>
      <Private>false</Private>
    </Reference>
  </ItemGroup>

  <!-- MSBuild targets for ATAS deployment -->
  <Target Name="CopyToATAS" AfterTargets="Build">
    <PropertyGroup>
      <ATASStrategyPath>$(ATASDeploymentPath)</ATASStrategyPath>
    </PropertyGroup>

    <MakeDir Directories="$(ATASStrategyPath)" Condition="!Exists('$(ATASStrategyPath)')" />

    <!-- Copy main strategy assembly -->
    <Copy SourceFiles="$(OutputPath)$(AssemblyName).dll" DestinationFolder="$(ATASStrategyPath)" />
    <Copy SourceFiles="$(OutputPath)$(AssemblyName).pdb" DestinationFolder="$(ATASStrategyPath)" Condition="Exists('$(OutputPath)$(AssemblyName).pdb')" />

    <Message Text="High-Probability Scalping v2.0 deployed to: $(ATASStrategyPath)" Importance="high" />
    <Message Text="Remember to refresh ATAS Chart Strategies dialog!" Importance="high" />
  </Target>

</Project>
