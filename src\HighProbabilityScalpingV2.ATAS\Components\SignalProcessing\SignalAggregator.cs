using System;
using System.Collections.Generic;
using System.Linq;
using HighProbabilityScalpingV2.ATAS.Components.Interfaces;
using HighProbabilityScalpingV2.ATAS.Components.Logging;
using HighProbabilityScalpingV2.ATAS.Components.Models;

namespace HighProbabilityScalpingV2.ATAS.Components.SignalProcessing
{
    /// <summary>
    /// Aggregates signals from multiple indicators to generate final trading decisions
    /// Implements weighted voting system with confidence thresholds
    /// Phase 2 component for coordinating VFI, RSI, Bollinger Bands, and Stochastic signals
    /// </summary>
    public class SignalAggregator
    {
        private readonly List<IIndicator> _indicators;
        private readonly SimpleLogger _logger;
        private readonly decimal _minimumConfidence;
        private readonly int _minimumIndicatorAlignment;

        // Performance tracking
        private long _aggregationCount;
        private DateTime _lastAggregationTime;
        private TimeSpan _lastAggregationDuration;

        // Signal statistics
        private long _totalSignals;
        private long _qualifiedSignals;
        private long _buySignals;
        private long _sellSignals;
        private long _neutralSignals;

        /// <summary>
        /// Creates a new signal aggregator
        /// </summary>
        /// <param name="logger">Logger for signal processing</param>
        /// <param name="minimumConfidence">Minimum confidence threshold (0.0 to 1.0)</param>
        /// <param name="minimumIndicatorAlignment">Minimum number of indicators that must agree</param>
        public SignalAggregator(SimpleLogger logger, decimal minimumConfidence = 0.85m, int minimumIndicatorAlignment = 3)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _minimumConfidence = Math.Max(0, Math.Min(1, minimumConfidence));
            _minimumIndicatorAlignment = Math.Max(1, minimumIndicatorAlignment);
            
            _indicators = new List<IIndicator>();
            _aggregationCount = 0;

            _logger.LogInfo($"🎯 Signal Aggregator initialized: MinConfidence={_minimumConfidence:P1}, MinAlignment={_minimumIndicatorAlignment}");
        }

        /// <summary>
        /// Adds an indicator to the aggregation system
        /// </summary>
        /// <param name="indicator">Indicator to add</param>
        public void AddIndicator(IIndicator indicator)
        {
            if (indicator == null)
                throw new ArgumentNullException(nameof(indicator));

            _indicators.Add(indicator);
            _logger.LogInfo($"📊 Added indicator: {indicator.Name} (Weight: {indicator.Weight:P1}, Category: {indicator.Category})");
        }

        /// <summary>
        /// Removes an indicator from the aggregation system
        /// </summary>
        /// <param name="indicatorName">Name of indicator to remove</param>
        /// <returns>True if indicator was found and removed</returns>
        public bool RemoveIndicator(string indicatorName)
        {
            var indicator = _indicators.FirstOrDefault(i => i.Name == indicatorName);
            if (indicator != null)
            {
                _indicators.Remove(indicator);
                _logger.LogInfo($"🗑️ Removed indicator: {indicatorName}");
                return true;
            }
            return false;
        }

        /// <summary>
        /// Aggregates signals from all indicators to generate final trading signal
        /// </summary>
        /// <param name="candle">Current candle data</param>
        /// <param name="marketData">Additional market data</param>
        /// <returns>Aggregated signal with combined confidence</returns>
        public IndicatorSignal AggregateSignals(IndicatorCandle candle, MarketData marketData)
        {
            var startTime = DateTime.UtcNow;
            _aggregationCount++;
            _totalSignals++;

            try
            {
                // Get signals from all enabled indicators
                var individualSignals = new List<IndicatorSignal>();
                var enabledIndicators = _indicators.Where(i => i.IsEnabled).ToList();

                if (enabledIndicators.Count == 0)
                {
                    _neutralSignals++;
                    return IndicatorSignal.Neutral("No enabled indicators", "SignalAggregator");
                }

                // Calculate signals from each indicator
                foreach (var indicator in enabledIndicators)
                {
                    try
                    {
                        if (indicator.IsReady)
                        {
                            var signal = indicator.Calculate(candle, marketData);
                            individualSignals.Add(signal);
                            
                            _logger.LogDebug($"📈 {indicator.Name}: {signal.Direction} @ {signal.ConfidencePercent:F1}% - {signal.Reason}");
                        }
                        else
                        {
                            _logger.LogDebug($"⏳ {indicator.Name}: Not ready (needs more data)");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"Error calculating signal for {indicator.Name}", ex);
                    }
                }

                // Check if we have enough signals
                if (individualSignals.Count == 0)
                {
                    _neutralSignals++;
                    return IndicatorSignal.Neutral("No ready indicators", "SignalAggregator");
                }

                // Aggregate the signals
                var aggregatedSignal = PerformSignalAggregation(individualSignals, enabledIndicators);

                // Update statistics
                switch (aggregatedSignal.Direction)
                {
                    case SignalDirection.Buy:
                        _buySignals++;
                        break;
                    case SignalDirection.Sell:
                        _sellSignals++;
                        break;
                    default:
                        _neutralSignals++;
                        break;
                }

                if (aggregatedSignal.Confidence >= _minimumConfidence)
                {
                    _qualifiedSignals++;
                }

                return aggregatedSignal;
            }
            finally
            {
                _lastAggregationDuration = DateTime.UtcNow - startTime;
                _lastAggregationTime = DateTime.UtcNow;
            }
        }

        /// <summary>
        /// Performs the actual signal aggregation using weighted voting
        /// </summary>
        /// <param name="signals">Individual indicator signals</param>
        /// <param name="indicators">Corresponding indicators</param>
        /// <returns>Aggregated signal</returns>
        private IndicatorSignal PerformSignalAggregation(List<IndicatorSignal> signals, List<IIndicator> indicators)
        {
            // Separate signals by direction
            var buySignals = signals.Where(s => s.Direction == SignalDirection.Buy).ToList();
            var sellSignals = signals.Where(s => s.Direction == SignalDirection.Sell).ToList();
            var neutralSignals = signals.Where(s => s.Direction == SignalDirection.Neutral).ToList();

            // Calculate weighted scores for each direction
            var buyScore = CalculateWeightedScore(buySignals, indicators);
            var sellScore = CalculateWeightedScore(sellSignals, indicators);

            // Determine final direction based on scores and alignment requirements
            var finalDirection = SignalDirection.Neutral;
            var finalConfidence = 0m;
            var finalReason = "";

            // Check buy signals
            if (buySignals.Count >= _minimumIndicatorAlignment && buyScore > sellScore)
            {
                finalDirection = SignalDirection.Buy;
                finalConfidence = buyScore;
                finalReason = $"BUY: {buySignals.Count} indicators aligned, score={buyScore:F3}";
            }
            // Check sell signals
            else if (sellSignals.Count >= _minimumIndicatorAlignment && sellScore > buyScore)
            {
                finalDirection = SignalDirection.Sell;
                finalConfidence = sellScore;
                finalReason = $"SELL: {sellSignals.Count} indicators aligned, score={sellScore:F3}";
            }
            // Neutral (insufficient alignment or conflicting signals)
            else
            {
                finalDirection = SignalDirection.Neutral;
                finalConfidence = 0;
                finalReason = $"NEUTRAL: Insufficient alignment (Buy:{buySignals.Count}, Sell:{sellSignals.Count}, Min:{_minimumIndicatorAlignment})";
            }

            // Create aggregated signal
            var aggregatedSignal = new IndicatorSignal(finalDirection, finalConfidence, finalReason, "SignalAggregator");

            // Add detailed metadata
            aggregatedSignal.Metadata["BuySignals"] = buySignals.Count;
            aggregatedSignal.Metadata["SellSignals"] = sellSignals.Count;
            aggregatedSignal.Metadata["NeutralSignals"] = neutralSignals.Count;
            aggregatedSignal.Metadata["BuyScore"] = buyScore;
            aggregatedSignal.Metadata["SellScore"] = sellScore;
            aggregatedSignal.Metadata["TotalIndicators"] = indicators.Count;
            aggregatedSignal.Metadata["EnabledIndicators"] = indicators.Count(i => i.IsEnabled);
            aggregatedSignal.Metadata["ReadyIndicators"] = indicators.Count(i => i.IsReady);
            aggregatedSignal.Metadata["MinimumAlignment"] = _minimumIndicatorAlignment;
            aggregatedSignal.Metadata["MinimumConfidence"] = _minimumConfidence;

            // Add individual signal details
            for (int i = 0; i < signals.Count; i++)
            {
                var signal = signals[i];
                var indicator = indicators.FirstOrDefault(ind => ind.Name == signal.IndicatorName);
                if (indicator != null)
                {
                    aggregatedSignal.Metadata[$"Signal_{indicator.Name}"] = new
                    {
                        Direction = signal.Direction.ToString(),
                        Confidence = signal.Confidence,
                        Weight = indicator.Weight,
                        Reason = signal.Reason
                    };
                }
            }

            return aggregatedSignal;
        }

        /// <summary>
        /// Calculates weighted score for signals in a specific direction
        /// </summary>
        /// <param name="signals">Signals in the same direction</param>
        /// <param name="indicators">All indicators</param>
        /// <returns>Weighted score</returns>
        private decimal CalculateWeightedScore(List<IndicatorSignal> signals, List<IIndicator> indicators)
        {
            decimal totalScore = 0;
            decimal totalWeight = 0;

            foreach (var signal in signals)
            {
                var indicator = indicators.FirstOrDefault(i => i.Name == signal.IndicatorName);
                if (indicator != null)
                {
                    var weightedConfidence = signal.Confidence * indicator.Weight;
                    totalScore += weightedConfidence;
                    totalWeight += indicator.Weight;
                }
            }

            // Normalize by total weight to get average weighted confidence
            return totalWeight > 0 ? totalScore / totalWeight : 0;
        }

        /// <summary>
        /// Gets aggregation statistics
        /// </summary>
        /// <returns>Statistics summary</returns>
        public string GetStatistics()
        {
            var qualificationRate = _totalSignals > 0 ? (decimal)_qualifiedSignals / _totalSignals * 100 : 0;
            var buyRate = _totalSignals > 0 ? (decimal)_buySignals / _totalSignals * 100 : 0;
            var sellRate = _totalSignals > 0 ? (decimal)_sellSignals / _totalSignals * 100 : 0;
            var neutralRate = _totalSignals > 0 ? (decimal)_neutralSignals / _totalSignals * 100 : 0;

            return $"Aggregations: {_aggregationCount:N0}, " +
                   $"Signals: {_totalSignals:N0}, " +
                   $"Qualified: {_qualifiedSignals:N0} ({qualificationRate:F1}%), " +
                   $"Buy: {buyRate:F1}%, Sell: {sellRate:F1}%, Neutral: {neutralRate:F1}%, " +
                   $"LastTime: {_lastAggregationDuration.TotalMilliseconds:F2}ms";
        }

        /// <summary>
        /// Gets list of all indicators
        /// </summary>
        /// <returns>Read-only list of indicators</returns>
        public IReadOnlyList<IIndicator> GetIndicators()
        {
            return _indicators.AsReadOnly();
        }

        /// <summary>
        /// Gets indicator by name
        /// </summary>
        /// <param name="name">Indicator name</param>
        /// <returns>Indicator or null if not found</returns>
        public IIndicator? GetIndicator(string name)
        {
            return _indicators.FirstOrDefault(i => i.Name == name);
        }

        /// <summary>
        /// Updates configuration
        /// </summary>
        /// <param name="minimumConfidence">New minimum confidence threshold</param>
        /// <param name="minimumIndicatorAlignment">New minimum indicator alignment</param>
        public void UpdateConfiguration(decimal minimumConfidence, int minimumIndicatorAlignment)
        {
            var oldMinConfidence = _minimumConfidence;
            var oldMinAlignment = _minimumIndicatorAlignment;

            // Note: These are readonly fields, so this method documents the intended interface
            // In a real implementation, these would be mutable properties

            _logger.LogInfo($"🔧 Configuration update requested: " +
                           $"MinConfidence: {oldMinConfidence:P1} -> {minimumConfidence:P1}, " +
                           $"MinAlignment: {oldMinAlignment} -> {minimumIndicatorAlignment}");
        }

        /// <summary>
        /// Performance metrics
        /// </summary>
        public long AggregationCount => _aggregationCount;
        public TimeSpan LastAggregationTime => _lastAggregationDuration;
        public DateTime LastUpdateTime => _lastAggregationTime;
        public decimal MinimumConfidence => _minimumConfidence;
        public int MinimumIndicatorAlignment => _minimumIndicatorAlignment;
    }
}
