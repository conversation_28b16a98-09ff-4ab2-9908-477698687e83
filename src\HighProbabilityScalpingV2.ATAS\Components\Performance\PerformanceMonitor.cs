using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using HighProbabilityScalpingV2.ATAS.Components.Logging;

namespace HighProbabilityScalpingV2.ATAS.Components.Performance
{
    /// <summary>
    /// High-performance monitoring system for tracking operation timings
    /// Designed to meet less than 20ms processing requirements for Phase 1
    /// </summary>
    public class PerformanceMonitor
    {
        private readonly SimpleLogger _logger;
        private readonly Dictionary<string, List<TimeSpan>> _timings;
        private readonly Dictionary<string, long> _counters;
        private readonly object _lockObject = new object();
        private readonly Stopwatch _globalStopwatch;

        // Performance targets
        private const double PHASE1_TARGET_MS = 5.0;
        private const double TOTAL_TARGET_MS = 20.0;
        private const int MAX_TIMING_HISTORY = 1000;

        /// <summary>
        /// Creates a new performance monitor
        /// </summary>
        /// <param name="logger">Logger for performance reporting</param>
        public PerformanceMonitor(SimpleLogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _timings = new Dictionary<string, List<TimeSpan>>();
            _counters = new Dictionary<string, long>();
            _globalStopwatch = Stopwatch.StartNew();

            _logger.LogInfo("⚡ Performance Monitor initialized");
            _logger.LogInfo($"🎯 Phase 1 Target: <{PHASE1_TARGET_MS}ms per operation");
            _logger.LogInfo($"🎯 Total Target: <{TOTAL_TARGET_MS}ms per calculation");
        }

        /// <summary>
        /// Starts timing an operation and returns a disposable scope
        /// </summary>
        /// <param name="operation">Name of the operation being timed</param>
        /// <returns>Disposable timing scope</returns>
        public IDisposable StartTiming(string operation)
        {
            return new TimingScope(operation, this);
        }

        /// <summary>
        /// Records a timing measurement for an operation
        /// </summary>
        /// <param name="operation">Operation name</param>
        /// <param name="duration">Duration of the operation</param>
        public void RecordTiming(string operation, TimeSpan duration)
        {
            lock (_lockObject)
            {
                // Initialize timing list if needed
                if (!_timings.ContainsKey(operation))
                {
                    _timings[operation] = new List<TimeSpan>();
                }

                // Add timing (keep only recent measurements)
                var timingList = _timings[operation];
                timingList.Add(duration);
                
                if (timingList.Count > MAX_TIMING_HISTORY)
                {
                    timingList.RemoveAt(0); // Remove oldest
                }

                // Increment counter
                if (!_counters.ContainsKey(operation))
                {
                    _counters[operation] = 0;
                }
                _counters[operation]++;

                // Check against targets and log warnings
                var durationMs = duration.TotalMilliseconds;
                
                if (operation.Contains("VFI") || operation.Contains("Indicator"))
                {
                    if (durationMs > PHASE1_TARGET_MS)
                    {
                        _logger.LogWarning($"⚠️ Performance: {operation} took {durationMs:F2}ms (target: <{PHASE1_TARGET_MS}ms)");
                    }
                }
                else if (operation.Contains("Total") || operation.Contains("Calculate"))
                {
                    if (durationMs > TOTAL_TARGET_MS)
                    {
                        _logger.LogWarning($"⚠️ Performance: {operation} took {durationMs:F2}ms (target: <{TOTAL_TARGET_MS}ms)");
                    }
                }

                // Log debug info for very slow operations
                if (durationMs > 50)
                {
                    _logger.LogError($"🚨 Critical Performance Issue: {operation} took {durationMs:F2}ms!");
                }
            }
        }

        /// <summary>
        /// Increments a simple counter
        /// </summary>
        /// <param name="counterName">Name of the counter</param>
        public void IncrementCounter(string counterName)
        {
            lock (_lockObject)
            {
                if (!_counters.ContainsKey(counterName))
                {
                    _counters[counterName] = 0;
                }
                _counters[counterName]++;
            }
        }

        /// <summary>
        /// Gets average timing for an operation
        /// </summary>
        /// <param name="operation">Operation name</param>
        /// <returns>Average duration, or TimeSpan.Zero if no data</returns>
        public TimeSpan GetAverageTiming(string operation)
        {
            lock (_lockObject)
            {
                if (!_timings.ContainsKey(operation) || _timings[operation].Count == 0)
                {
                    return TimeSpan.Zero;
                }

                var average = _timings[operation].Average(t => t.TotalMilliseconds);
                return TimeSpan.FromMilliseconds(average);
            }
        }

        /// <summary>
        /// Gets maximum timing for an operation
        /// </summary>
        /// <param name="operation">Operation name</param>
        /// <returns>Maximum duration, or TimeSpan.Zero if no data</returns>
        public TimeSpan GetMaxTiming(string operation)
        {
            lock (_lockObject)
            {
                if (!_timings.ContainsKey(operation) || _timings[operation].Count == 0)
                {
                    return TimeSpan.Zero;
                }

                return _timings[operation].Max();
            }
        }

        /// <summary>
        /// Gets count for an operation or counter
        /// </summary>
        /// <param name="name">Operation or counter name</param>
        /// <returns>Count value</returns>
        public long GetCount(string name)
        {
            lock (_lockObject)
            {
                return _counters.ContainsKey(name) ? _counters[name] : 0;
            }
        }

        /// <summary>
        /// Logs a comprehensive performance report
        /// </summary>
        public void LogPerformanceReport()
        {
            lock (_lockObject)
            {
                _logger.LogSeparator("PERFORMANCE REPORT");
                
                var uptime = _globalStopwatch.Elapsed;
                _logger.LogPerformance($"📊 Uptime: {uptime.TotalMinutes:F1} minutes");

                if (_timings.Any())
                {
                    _logger.LogPerformance("⏱️ Operation Timings:");
                    
                    foreach (var kvp in _timings.OrderBy(x => x.Key))
                    {
                        var operation = kvp.Key;
                        var timings = kvp.Value;
                        
                        if (timings.Count > 0)
                        {
                            var avg = timings.Average(t => t.TotalMilliseconds);
                            var max = timings.Max(t => t.TotalMilliseconds);
                            var min = timings.Min(t => t.TotalMilliseconds);
                            var count = timings.Count;
                            
                            var status = GetPerformanceStatus(operation, avg);
                            _logger.LogPerformance($"  {status} {operation}: Avg {avg:F2}ms, Max {max:F2}ms, Min {min:F2}ms, Count {count}");
                        }
                    }
                }

                if (_counters.Any())
                {
                    _logger.LogPerformance("📈 Counters:");
                    foreach (var kvp in _counters.OrderBy(x => x.Key))
                    {
                        _logger.LogPerformance($"  📊 {kvp.Key}: {kvp.Value:N0}");
                    }
                }

                _logger.LogSeparator();
            }
        }

        /// <summary>
        /// Gets performance status emoji based on timing
        /// </summary>
        /// <param name="operation">Operation name</param>
        /// <param name="averageMs">Average timing in milliseconds</param>
        /// <returns>Status emoji</returns>
        private string GetPerformanceStatus(string operation, double averageMs)
        {
            var target = operation.Contains("VFI") || operation.Contains("Indicator") ? PHASE1_TARGET_MS : TOTAL_TARGET_MS;
            
            if (averageMs <= target * 0.5)
                return "🟢"; // Excellent
            else if (averageMs <= target)
                return "🟡"; // Good
            else if (averageMs <= target * 2)
                return "🟠"; // Warning
            else
                return "🔴"; // Critical
        }

        /// <summary>
        /// Logs a quick performance summary
        /// </summary>
        public void LogQuickSummary()
        {
            lock (_lockObject)
            {
                var totalCalculations = GetCount("TotalCalculation");
                var vfiCalculations = GetCount("VFI_Calculation");
                
                if (totalCalculations > 0)
                {
                    var avgTotal = GetAverageTiming("TotalCalculation").TotalMilliseconds;
                    var avgVfi = GetAverageTiming("VFI_Calculation").TotalMilliseconds;
                    
                    _logger.LogPerformance($"📊 Quick Summary: Total {avgTotal:F1}ms, VFI {avgVfi:F1}ms, Calculations {totalCalculations}");
                }
            }
        }

        /// <summary>
        /// Clears all performance data
        /// </summary>
        public void Reset()
        {
            lock (_lockObject)
            {
                _timings.Clear();
                _counters.Clear();
                _globalStopwatch.Restart();
                _logger.LogInfo("🔄 Performance Monitor reset");
            }
        }
    }

    /// <summary>
    /// Disposable timing scope for automatic timing measurement
    /// </summary>
    public class TimingScope : IDisposable
    {
        private readonly string _operation;
        private readonly PerformanceMonitor _monitor;
        private readonly Stopwatch _stopwatch;

        /// <summary>
        /// Creates a new timing scope
        /// </summary>
        /// <param name="operation">Operation being timed</param>
        /// <param name="monitor">Performance monitor to report to</param>
        public TimingScope(string operation, PerformanceMonitor monitor)
        {
            _operation = operation ?? throw new ArgumentNullException(nameof(operation));
            _monitor = monitor ?? throw new ArgumentNullException(nameof(monitor));
            _stopwatch = Stopwatch.StartNew();
        }

        /// <summary>
        /// Completes timing and reports duration
        /// </summary>
        public void Dispose()
        {
            _stopwatch.Stop();
            _monitor.RecordTiming(_operation, _stopwatch.Elapsed);
        }
    }
}
