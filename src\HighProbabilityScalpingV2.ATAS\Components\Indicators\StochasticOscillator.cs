using System;
using System.Collections.Generic;
using HighProbabilityScalpingV2.ATAS.Components.Interfaces;
using HighProbabilityScalpingV2.ATAS.Components.Models;
using HighProbabilityScalpingV2.ATAS.Components.Utils;

namespace HighProbabilityScalpingV2.ATAS.Components.Indicators
{
    /// <summary>
    /// Stochastic Oscillator indicator implementation
    /// 14-period %K with 3-period %D smoothing, 80/20 thresholds
    /// Phase 2 indicator with 15% weight in signal generation
    /// </summary>
    public class StochasticOscillator : IIndicator
    {
        private readonly CircularBuffer<decimal> _highs;
        private readonly CircularBuffer<decimal> _lows;
        private readonly CircularBuffer<decimal> _closes;
        private readonly CircularBuffer<decimal> _kValues; // %K values
        private readonly CircularBuffer<decimal> _dValues; // %D values (smoothed %K)
        private readonly int _kPeriod;
        private readonly int _dPeriod;
        private readonly decimal _overboughtLevel;
        private readonly decimal _oversoldLevel;

        // Performance tracking
        private DateTime _lastCalculationTime;
        private TimeSpan _lastCalculationDuration;
        private long _calculationCount;

        // IIndicator interface properties
        private decimal _weight = 0.15m; // 15% weight for Phase 2
        private bool _isEnabled = true;

        /// <summary>
        /// Creates a new Stochastic Oscillator indicator
        /// </summary>
        /// <param name="kPeriod">Period for %K calculation (default: 14)</param>
        /// <param name="dPeriod">Period for %D smoothing (default: 3)</param>
        /// <param name="overboughtLevel">Overbought threshold (default: 80)</param>
        /// <param name="oversoldLevel">Oversold threshold (default: 20)</param>
        public StochasticOscillator(int kPeriod = 14, int dPeriod = 3, 
            decimal overboughtLevel = 80m, decimal oversoldLevel = 20m)
        {
            if (kPeriod < 1)
                throw new ArgumentException("K period must be at least 1", nameof(kPeriod));
            if (dPeriod < 1)
                throw new ArgumentException("D period must be at least 1", nameof(dPeriod));
            if (overboughtLevel <= oversoldLevel)
                throw new ArgumentException("Overbought level must be greater than oversold level");
            if (overboughtLevel > 100 || oversoldLevel < 0)
                throw new ArgumentException("Stochastic levels must be between 0 and 100");

            _kPeriod = kPeriod;
            _dPeriod = dPeriod;
            _overboughtLevel = overboughtLevel;
            _oversoldLevel = oversoldLevel;

            // Initialize circular buffers
            _highs = new CircularBuffer<decimal>(kPeriod);
            _lows = new CircularBuffer<decimal>(kPeriod);
            _closes = new CircularBuffer<decimal>(kPeriod);
            _kValues = new CircularBuffer<decimal>(dPeriod); // For %D calculation
            _dValues = new CircularBuffer<decimal>(100); // Keep history for trend analysis

            _calculationCount = 0;
        }

        /// <summary>
        /// Calculates Stochastic values and generates trading signal (IIndicator interface)
        /// </summary>
        /// <param name="candle">Current candle data</param>
        /// <param name="marketData">Additional market data (unused in Phase 2)</param>
        /// <returns>Trading signal with confidence level</returns>
        public IndicatorSignal Calculate(IndicatorCandle candle, MarketData marketData)
        {
            var startTime = DateTime.UtcNow;
            _calculationCount++;

            try
            {
                if (!IsEnabled)
                {
                    return IndicatorSignal.Neutral("Indicator disabled", Name);
                }

                // Add candle data
                _highs.Add(candle.High);
                _lows.Add(candle.Low);
                _closes.Add(candle.Close);

                // Check if we have enough data for %K
                if (!IsKReady)
                {
                    return IndicatorSignal.Neutral("Insufficient data for Stochastic %K calculation", Name);
                }

                // Calculate %K
                var kValue = CalculateK();
                _kValues.Add(kValue);

                // Check if we have enough data for %D
                if (!IsReady)
                {
                    return IndicatorSignal.Neutral("Insufficient data for Stochastic %D calculation", Name);
                }

                // Calculate %D (smoothed %K)
                var dValue = CalculateD();
                _dValues.Add(dValue);

                // Generate signal
                var signal = GenerateSignal(kValue, dValue, candle);

                // Add metadata
                signal.Metadata["K"] = kValue;
                signal.Metadata["D"] = dValue;
                signal.Metadata["KPeriod"] = _kPeriod;
                signal.Metadata["DPeriod"] = _dPeriod;
                signal.Metadata["OverboughtLevel"] = _overboughtLevel;
                signal.Metadata["OversoldLevel"] = _oversoldLevel;

                return signal;
            }
            finally
            {
                // Track performance
                _lastCalculationDuration = DateTime.UtcNow - startTime;
                _lastCalculationTime = DateTime.UtcNow;
            }
        }

        /// <summary>
        /// Calculates %K value
        /// </summary>
        /// <returns>%K value (0-100)</returns>
        private decimal CalculateK()
        {
            // Find highest high and lowest low over the period
            var highestHigh = decimal.MinValue;
            var lowestLow = decimal.MaxValue;

            for (int i = 0; i < _highs.Count; i++)
            {
                if (_highs[i] > highestHigh)
                    highestHigh = _highs[i];
                if (_lows[i] < lowestLow)
                    lowestLow = _lows[i];
            }

            var currentClose = _closes.Latest;

            // Calculate %K
            if (highestHigh == lowestLow)
                return 50; // Avoid division by zero, return neutral

            var k = ((currentClose - lowestLow) / (highestHigh - lowestLow)) * 100;
            return Math.Max(0, Math.Min(100, k)); // Clamp to 0-100
        }

        /// <summary>
        /// Calculates %D value (smoothed %K)
        /// </summary>
        /// <returns>%D value (0-100)</returns>
        private decimal CalculateD()
        {
            // %D is the simple moving average of %K values
            return _kValues.Average();
        }

        /// <summary>
        /// Generates trading signal based on Stochastic values
        /// </summary>
        /// <param name="k">Current %K value</param>
        /// <param name="d">Current %D value</param>
        /// <param name="candle">Current candle for context</param>
        /// <returns>Trading signal</returns>
        private IndicatorSignal GenerateSignal(decimal k, decimal d, IndicatorCandle candle)
        {
            var reason = $"Stoch: %K={k:F1}, %D={d:F1}";

            // Check for crossover signals first (higher priority)
            if (_kValues.Count >= 2 && _dValues.Count >= 2)
            {
                var prevK = _kValues[_kValues.Count - 2];
                var prevD = _dValues[_dValues.Count - 2];

                // Bullish crossover: %K crosses above %D in oversold region
                if (k > d && prevK <= prevD && k <= _oversoldLevel + 10) // Within 10 points of oversold
                {
                    var confidence = CalculateCrossoverConfidence(k, d, true);
                    return IndicatorSignal.Buy(confidence, $"{reason} (Bullish Crossover)", Name, k);
                }

                // Bearish crossover: %K crosses below %D in overbought region
                if (k < d && prevK >= prevD && k >= _overboughtLevel - 10) // Within 10 points of overbought
                {
                    var confidence = CalculateCrossoverConfidence(k, d, false);
                    return IndicatorSignal.Sell(confidence, $"{reason} (Bearish Crossover)", Name, k);
                }
            }

            // Overbought/Oversold signals (lower priority)
            // Sell signal (overbought)
            if (k >= _overboughtLevel && d >= _overboughtLevel)
            {
                var confidence = CalculateOverboughtConfidence(k, d);
                return IndicatorSignal.Sell(confidence, reason, Name, k);
            }

            // Buy signal (oversold)
            if (k <= _oversoldLevel && d <= _oversoldLevel)
            {
                var confidence = CalculateOversoldConfidence(k, d);
                return IndicatorSignal.Buy(confidence, reason, Name, k);
            }

            // Neutral signal
            return IndicatorSignal.Neutral(reason, Name);
        }

        /// <summary>
        /// Calculates confidence for crossover signals
        /// </summary>
        /// <param name="k">Current %K value</param>
        /// <param name="d">Current %D value</param>
        /// <param name="isBuySignal">Whether this is a buy signal</param>
        /// <returns>Confidence level (0.0 to 1.0)</returns>
        private decimal CalculateCrossoverConfidence(decimal k, decimal d, bool isBuySignal)
        {
            // Base confidence for crossovers is higher
            var baseConfidence = 0.7m;
            
            // Bonus for crossovers in extreme regions
            if (isBuySignal && k <= _oversoldLevel + 5)
            {
                baseConfidence += 0.15m; // 15% bonus for oversold crossover
            }
            else if (!isBuySignal && k >= _overboughtLevel - 5)
            {
                baseConfidence += 0.15m; // 15% bonus for overbought crossover
            }

            // Bonus for strong divergence between %K and %D
            var divergence = Math.Abs(k - d);
            var divergenceBonus = Math.Min(0.1m, divergence * 0.01m); // Max 10% bonus
            
            var totalConfidence = baseConfidence + divergenceBonus;
            
            return Math.Min(0.95m, totalConfidence);
        }

        /// <summary>
        /// Calculates confidence for overbought (sell) signals
        /// </summary>
        /// <param name="k">Current %K value</param>
        /// <param name="d">Current %D value</param>
        /// <returns>Confidence level (0.0 to 1.0)</returns>
        private decimal CalculateOverboughtConfidence(decimal k, decimal d)
        {
            // Base confidence starts at 60%
            var baseConfidence = 0.6m;
            
            // Higher confidence when both %K and %D are well above overbought level
            var avgValue = (k + d) / 2;
            var excessValue = avgValue - _overboughtLevel;
            var bonusConfidence = Math.Min(0.25m, excessValue * 0.01m); // Max 25% bonus
            
            var totalConfidence = baseConfidence + bonusConfidence;
            
            return Math.Min(0.85m, Math.Max(0.6m, totalConfidence)); // Cap at 85% for non-crossover
        }

        /// <summary>
        /// Calculates confidence for oversold (buy) signals
        /// </summary>
        /// <param name="k">Current %K value</param>
        /// <param name="d">Current %D value</param>
        /// <returns>Confidence level (0.0 to 1.0)</returns>
        private decimal CalculateOversoldConfidence(decimal k, decimal d)
        {
            // Base confidence starts at 60%
            var baseConfidence = 0.6m;
            
            // Higher confidence when both %K and %D are well below oversold level
            var avgValue = (k + d) / 2;
            var excessValue = _oversoldLevel - avgValue;
            var bonusConfidence = Math.Min(0.25m, excessValue * 0.01m); // Max 25% bonus
            
            var totalConfidence = baseConfidence + bonusConfidence;
            
            return Math.Min(0.85m, Math.Max(0.6m, totalConfidence)); // Cap at 85% for non-crossover
        }

        /// <summary>
        /// Resets the indicator state and clears all historical data
        /// </summary>
        public void Reset()
        {
            _highs.Clear();
            _lows.Clear();
            _closes.Clear();
            _kValues.Clear();
            _dValues.Clear();
            _calculationCount = 0;
            _lastCalculationTime = DateTime.MinValue;
            _lastCalculationDuration = TimeSpan.Zero;
        }

        #region IIndicator Interface Implementation

        /// <summary>
        /// Name of the indicator
        /// </summary>
        public string Name => "StochasticOscillator";

        /// <summary>
        /// Category of the indicator
        /// </summary>
        public string Category => "Momentum";

        /// <summary>
        /// Weight of this indicator in signal aggregation
        /// </summary>
        public decimal Weight 
        { 
            get => _weight; 
            set => _weight = Math.Max(0, Math.Min(1, value)); 
        }

        /// <summary>
        /// Whether this indicator is enabled for calculations
        /// </summary>
        public bool IsEnabled 
        { 
            get => _isEnabled; 
            set => _isEnabled = value; 
        }

        /// <summary>
        /// Whether the indicator has enough data to generate reliable signals
        /// </summary>
        public bool IsReady => IsKReady && _kValues.Count >= _dPeriod;

        /// <summary>
        /// Whether %K calculation is ready
        /// </summary>
        public bool IsKReady => _highs.Count >= _kPeriod && _lows.Count >= _kPeriod && _closes.Count >= _kPeriod;

        /// <summary>
        /// Duration of the last calculation for performance monitoring
        /// </summary>
        public TimeSpan LastCalculationTime => _lastCalculationDuration;

        /// <summary>
        /// Gets current operational status of the indicator
        /// </summary>
        /// <returns>Status information for monitoring and debugging</returns>
        public IndicatorStatus GetStatus()
        {
            return new IndicatorStatus
            {
                IsOperational = IsEnabled && IsReady,
                StatusMessage = IsReady ? "Ready" : "Insufficient data",
                DataPointsRequired = _kPeriod + _dPeriod - 1,
                DataPointsAvailable = Math.Min(_highs.Count, Math.Min(_lows.Count, _closes.Count)),
                LastUpdate = _lastCalculationTime,
                Metadata = new Dictionary<string, object>
                {
                    ["CurrentK"] = _kValues.Count > 0 ? _kValues.Latest : 0,
                    ["CurrentD"] = _dValues.Count > 0 ? _dValues.Latest : 0,
                    ["CalculationCount"] = _calculationCount,
                    ["LastCalculationTimeMs"] = LastCalculationTime.TotalMilliseconds,
                    ["KPeriod"] = _kPeriod,
                    ["DPeriod"] = _dPeriod,
                    ["OverboughtLevel"] = _overboughtLevel,
                    ["OversoldLevel"] = _oversoldLevel,
                    ["Weight"] = Weight,
                    ["IsEnabled"] = IsEnabled
                }
            };
        }

        #endregion

        #region Public Properties

        /// <summary>
        /// Current %K value
        /// </summary>
        public decimal CurrentK => _kValues.Count > 0 ? _kValues.Latest : 0;

        /// <summary>
        /// Current %D value
        /// </summary>
        public decimal CurrentD => _dValues.Count > 0 ? _dValues.Latest : 0;

        /// <summary>
        /// Number of calculations performed
        /// </summary>
        public long CalculationCount => _calculationCount;

        /// <summary>
        /// Timestamp of last update
        /// </summary>
        public DateTime LastUpdateTime => _lastCalculationTime;

        /// <summary>
        /// Indicator configuration
        /// </summary>
        public int KPeriod => _kPeriod;
        public int DPeriod => _dPeriod;
        public decimal OverboughtLevel => _overboughtLevel;
        public decimal OversoldLevel => _oversoldLevel;

        #endregion
    }
}
