using ATAS.Indicators;
using ATAS.Indicators.Drawing;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace HighProbabilityScalpingV2.ATAS.Components.Indicators
{
    /// <summary>
    /// ATAS-Compatible Ultra-Fast RSI Indicator
    /// Wraps our custom RSI logic in ATAS indicator pattern
    /// </summary>
    [DisplayName("Ultra-Fast RSI")]
    public class ATASUltraFastRSI : Indicator
    {
        #region Fields
        private readonly ValueDataSeries _rsiSeries;
        private readonly ValueDataSeries _signalSeries;
        
        // Calculation fields
        private int _period = 3;
        private decimal _overboughtThreshold = 80m;
        private decimal _oversoldThreshold = 20m;
        
        // Data storage
        private readonly CircularBuffer<decimal> _prices;
        private readonly CircularBuffer<decimal> _gains;
        private readonly CircularBuffer<decimal> _losses;
        
        private decimal _lastRSI = 50m;
        private decimal _averageGain = 0m;
        private decimal _averageLoss = 0m;
        #endregion

        #region Properties
        [Display(Name = "Period", Order = 10)]
        public int Period
        {
            get => _period;
            set
            {
                _period = Math.Max(1, value);
                RecalculateValues();
            }
        }

        [Display(Name = "Overbought", Order = 20)]
        public decimal OverboughtThreshold
        {
            get => _overboughtThreshold;
            set
            {
                _overboughtThreshold = value;
                RecalculateValues();
            }
        }

        [Display(Name = "Oversold", Order = 30)]
        public decimal OversoldThreshold
        {
            get => _oversoldThreshold;
            set
            {
                _oversoldThreshold = value;
                RecalculateValues();
            }
        }
        #endregion

        #region Constructor
        public ATASUltraFastRSI()
            : base(true)
        {
            // Initialize data series for visualization
            _rsiSeries = (ValueDataSeries)DataSeries[0];
            _rsiSeries.Name = "RSI";
            _rsiSeries.Color = System.Drawing.Color.Purple;
            _rsiSeries.VisualType = VisualMode.Line;

            _signalSeries = new ValueDataSeries("Signal")
            {
                Color = System.Drawing.Color.Orange,
                VisualType = VisualMode.Histogram
            };
            DataSeries.Add(_signalSeries);

            // Initialize circular buffers
            _prices = new CircularBuffer<decimal>(_period + 1);
            _gains = new CircularBuffer<decimal>(_period);
            _losses = new CircularBuffer<decimal>(_period);
        }
        #endregion

        #region ATAS Indicator Implementation
        protected override void OnCalculate(int bar, decimal value)
        {
            // Use the provided value (typically close price)
            var currentPrice = value;
            _prices.Add(currentPrice);

            // Calculate price change if we have previous price
            if (_prices.Count >= 2)
            {
                var previousPrice = _prices[_prices.Count - 2];
                var priceChange = currentPrice - previousPrice;

                // Separate gains and losses
                var gain = priceChange > 0 ? priceChange : 0m;
                var loss = priceChange < 0 ? Math.Abs(priceChange) : 0m;

                _gains.Add(gain);
                _losses.Add(loss);

                // Calculate RSI if we have enough data
                if (_gains.Count >= _period && _losses.Count >= _period)
                {
                    // Calculate average gain and loss
                    var totalGain = 0m;
                    var totalLoss = 0m;

                    for (int i = 0; i < _period; i++)
                    {
                        totalGain += _gains[_gains.Count - 1 - i];
                        totalLoss += _losses[_losses.Count - 1 - i];
                    }

                    _averageGain = totalGain / _period;
                    _averageLoss = totalLoss / _period;

                    // Calculate RSI
                    if (_averageLoss == 0)
                    {
                        _lastRSI = 100m;
                    }
                    else
                    {
                        var rs = _averageGain / _averageLoss;
                        _lastRSI = 100m - (100m / (1m + rs));
                    }

                    // Set values for visualization
                    _rsiSeries[bar] = _lastRSI;

                    // Generate signal
                    var signal = _lastRSI >= _overboughtThreshold ? -1m :
                                _lastRSI <= _oversoldThreshold ? 1m : 0m;
                    _signalSeries[bar] = signal;
                }
            }
        }

        /// <summary>
        /// Gets the RSI value for a specific bar
        /// </summary>
        public decimal GetRSI(int bar)
        {
            return _rsiSeries[bar];
        }

        /// <summary>
        /// Gets the current RSI value
        /// </summary>
        public decimal CurrentRSI => _lastRSI;

        /// <summary>
        /// Checks if indicator is ready (has enough data)
        /// </summary>
        public bool IsReady => _gains.Count >= _period && _losses.Count >= _period;
        #endregion
    }
}
