using System;
using System.Collections.Generic;
using System.Linq;

namespace HighProbabilityScalpingV2.ATAS.Components.Utils
{
    /// <summary>
    /// High-performance circular buffer for storing fixed-size collections
    /// Optimized for indicator calculations with minimal memory allocations
    /// </summary>
    /// <typeparam name="T">Type of elements to store</typeparam>
    public class CircularBuffer<T>
    {
        private readonly T[] _buffer;
        private readonly int _capacity;
        private int _count;
        private int _head;

        /// <summary>
        /// Initializes a new circular buffer with specified capacity
        /// </summary>
        /// <param name="capacity">Maximum number of elements to store</param>
        public CircularBuffer(int capacity)
        {
            if (capacity <= 0)
                throw new ArgumentException("Capacity must be positive", nameof(capacity));

            _capacity = capacity;
            _buffer = new T[capacity];
            _count = 0;
            _head = 0;
        }

        /// <summary>
        /// Adds an item to the buffer, overwriting oldest item if at capacity
        /// </summary>
        /// <param name="item">Item to add</param>
        public void Add(T item)
        {
            _buffer[_head] = item;
            _head = (_head + 1) % _capacity;
            
            if (_count < _capacity)
                _count++;
        }

        /// <summary>
        /// Gets element at specified index (0 = oldest, Count-1 = newest)
        /// </summary>
        /// <param name="index">Index of element to retrieve</param>
        /// <returns>Element at specified index</returns>
        public T this[int index]
        {
            get
            {
                if (index < 0 || index >= _count)
                    throw new IndexOutOfRangeException($"Index {index} is out of range [0, {_count})");

                var actualIndex = (_head - _count + index + _capacity) % _capacity;
                return _buffer[actualIndex];
            }
        }

        /// <summary>
        /// Gets the most recently added element
        /// </summary>
        public T Latest
        {
            get
            {
                if (_count == 0)
                    throw new InvalidOperationException("Buffer is empty");

                var latestIndex = (_head - 1 + _capacity) % _capacity;
                return _buffer[latestIndex];
            }
        }

        /// <summary>
        /// Number of elements currently in the buffer
        /// </summary>
        public int Count => _count;

        /// <summary>
        /// Maximum capacity of the buffer
        /// </summary>
        public int Capacity => _capacity;

        /// <summary>
        /// Whether the buffer is at full capacity
        /// </summary>
        public bool IsFull => _count == _capacity;

        /// <summary>
        /// Whether the buffer is empty
        /// </summary>
        public bool IsEmpty => _count == 0;

        /// <summary>
        /// Converts buffer contents to array (oldest to newest)
        /// </summary>
        /// <returns>Array containing all elements in chronological order</returns>
        public T[] ToArray()
        {
            var result = new T[_count];
            for (int i = 0; i < _count; i++)
            {
                result[i] = this[i];
            }
            return result;
        }

        /// <summary>
        /// Calculates sum of numeric elements (for decimal type)
        /// </summary>
        /// <returns>Sum of all elements</returns>
        public decimal Sum()
        {
            decimal sum = 0;
            for (int i = 0; i < _count; i++)
            {
                if (this[i] is decimal value)
                    sum += value;
            }
            return sum;
        }

        /// <summary>
        /// Calculates average of numeric elements (for decimal type)
        /// </summary>
        /// <returns>Average of all elements</returns>
        public decimal Average()
        {
            if (_count == 0)
                return 0;

            return Sum() / _count;
        }

        /// <summary>
        /// Clears all elements from the buffer
        /// </summary>
        public void Clear()
        {
            _count = 0;
            _head = 0;
            Array.Clear(_buffer, 0, _capacity);
        }

        /// <summary>
        /// Gets enumerable of all elements (oldest to newest)
        /// </summary>
        /// <returns>Enumerable of elements</returns>
        public IEnumerable<T> GetElements()
        {
            for (int i = 0; i < _count; i++)
            {
                yield return this[i];
            }
        }
    }
}
