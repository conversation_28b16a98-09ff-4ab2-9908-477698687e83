<?xml version="1.0"?>
<doc>
    <assembly>
        <name>HighProbabilityScalpingV2.ATAS</name>
    </assembly>
    <members>
        <member name="T:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex">
            <summary>
            Volume Flow Index (VFI) indicator implementation
            Optimized for ultra-low timeframe scalping with less than 5ms performance target
            Primary indicator for Phase 1 with 30% weight in signal generation
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.#ctor(System.Int32,System.Decimal,System.Decimal)">
            <summary>
            Creates a new Volume Flow Index indicator
            </summary>
            <param name="period">Period for VFI calculation (default: 14)</param>
            <param name="buyThreshold">Threshold for buy signals (default: 1.3)</param>
            <param name="sellThreshold">Threshold for sell signals (default: 0.7)</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.Calculate(HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle)">
            <summary>
            Calculates VFI value and generates trading signal
            </summary>
            <param name="candle">Current candle data</param>
            <returns>Trading signal with confidence level</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.CalculateVFI">
            <summary>
            Calculates the Volume Flow Index value
            </summary>
            <returns>VFI value</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.GenerateSignal(System.Decimal,HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle)">
            <summary>
            Generates trading signal based on VFI value
            </summary>
            <param name="vfi">Current VFI value</param>
            <param name="candle">Current candle for context</param>
            <returns>Trading signal</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.CalculateBuyConfidence(System.Decimal)">
            <summary>
            Calculates confidence for buy signals
            </summary>
            <param name="vfi">Current VFI value</param>
            <returns>Confidence level (0.0 to 1.0)</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.CalculateSellConfidence(System.Decimal)">
            <summary>
            Calculates confidence for sell signals
            </summary>
            <param name="vfi">Current VFI value</param>
            <returns>Confidence level (0.0 to 1.0)</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.CalculateTrendBoost(System.Boolean)">
            <summary>
            Calculates trend boost for confidence
            </summary>
            <param name="isBuySignal">Whether this is for a buy signal</param>
            <returns>Trend boost value</returns>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.IsReady">
            <summary>
            Whether the indicator has enough data to generate signals
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.CurrentValue">
            <summary>
            Current VFI value (latest calculated)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.LastCalculationTime">
            <summary>
            Performance metrics
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.Period">
            <summary>
            Indicator configuration
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.GetStatus">
            <summary>
            Gets indicator status for debugging
            </summary>
            <returns>Status string</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Indicators.VolumeFlowIndex.Reset">
            <summary>
            Resets the indicator state
            </summary>
        </member>
        <member name="T:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger">
            <summary>
            Simple, high-performance logging system for ATAS strategies
            Avoids complex dependencies and provides thread-safe file logging
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.#ctor(System.String,System.Boolean)">
            <summary>
            Creates a new simple logger instance
            </summary>
            <param name="strategyName">Name of the strategy for log identification</param>
            <param name="enableDebug">Whether to enable debug-level logging</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.LogInfo(System.String)">
            <summary>
            Logs an informational message
            </summary>
            <param name="message">Message to log</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.LogWarning(System.String)">
            <summary>
            Logs a warning message
            </summary>
            <param name="message">Warning message to log</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.LogError(System.String)">
            <summary>
            Logs an error message
            </summary>
            <param name="message">Error message to log</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.LogError(System.String,System.Exception)">
            <summary>
            Logs an error with exception details
            </summary>
            <param name="message">Error message</param>
            <param name="exception">Exception to log</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.LogDebug(System.String)">
            <summary>
            Logs a debug message (only if debug logging is enabled)
            </summary>
            <param name="message">Debug message to log</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.LogSignal(System.String)">
            <summary>
            Logs a signal-related message with special formatting
            </summary>
            <param name="message">Signal message to log</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.LogPerformance(System.String)">
            <summary>
            Logs a performance-related message
            </summary>
            <param name="message">Performance message to log</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.LogTrade(System.String)">
            <summary>
            Logs a trade-related message
            </summary>
            <param name="message">Trade message to log</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.WriteLog(System.String,System.String)">
            <summary>
            Core logging method that writes to file
            </summary>
            <param name="level">Log level</param>
            <param name="message">Message to log</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.LogSeparator(System.String)">
            <summary>
            Logs a separator line for visual organization
            </summary>
            <param name="title">Optional title for the separator</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.LogStartup(System.String,System.String)">
            <summary>
            Logs strategy startup information
            </summary>
            <param name="version">Strategy version</param>
            <param name="phase">Current phase</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.LogShutdown">
            <summary>
            Logs strategy shutdown information
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.LogFilePath">
            <summary>
            Gets the current log file path
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger.IsDebugEnabled">
            <summary>
            Whether debug logging is enabled
            </summary>
        </member>
        <member name="T:HighProbabilityScalpingV2.ATAS.Components.Models.SignalDirection">
            <summary>
            Represents the direction of a trading signal
            </summary>
        </member>
        <member name="F:HighProbabilityScalpingV2.ATAS.Components.Models.SignalDirection.Neutral">
            <summary>
            No clear direction - neutral signal
            </summary>
        </member>
        <member name="F:HighProbabilityScalpingV2.ATAS.Components.Models.SignalDirection.Buy">
            <summary>
            Bullish signal - suggests buying
            </summary>
        </member>
        <member name="F:HighProbabilityScalpingV2.ATAS.Components.Models.SignalDirection.Sell">
            <summary>
            Bearish signal - suggests selling
            </summary>
        </member>
        <member name="T:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal">
            <summary>
            Represents a trading signal generated by an indicator
            Contains direction, confidence level, and reasoning
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.Direction">
            <summary>
            Direction of the signal (Buy, Sell, or Neutral)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.Confidence">
            <summary>
            Confidence level of the signal (0.0 to 1.0)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.Timestamp">
            <summary>
            Timestamp when the signal was generated
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.Reason">
            <summary>
            Human-readable reason for the signal
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.IndicatorName">
            <summary>
            Name of the indicator that generated this signal
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.RawValue">
            <summary>
            Raw indicator value that generated the signal
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.#ctor(HighProbabilityScalpingV2.ATAS.Components.Models.SignalDirection,System.Decimal,System.String,System.String,System.Decimal)">
            <summary>
            Creates a new indicator signal
            </summary>
            <param name="direction">Signal direction</param>
            <param name="confidence">Confidence level (0.0 to 1.0)</param>
            <param name="reason">Reason for the signal</param>
            <param name="indicatorName">Name of the indicator</param>
            <param name="rawValue">Raw indicator value</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.Neutral(System.String,System.String)">
            <summary>
            Creates a neutral signal with specified reason
            </summary>
            <param name="reason">Reason for neutral signal</param>
            <param name="indicatorName">Name of the indicator</param>
            <returns>Neutral signal</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.Buy(System.Decimal,System.String,System.String,System.Decimal)">
            <summary>
            Creates a buy signal with specified confidence
            </summary>
            <param name="confidence">Confidence level</param>
            <param name="reason">Reason for buy signal</param>
            <param name="indicatorName">Name of the indicator</param>
            <param name="rawValue">Raw indicator value</param>
            <returns>Buy signal</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.Sell(System.Decimal,System.String,System.String,System.Decimal)">
            <summary>
            Creates a sell signal with specified confidence
            </summary>
            <param name="confidence">Confidence level</param>
            <param name="reason">Reason for sell signal</param>
            <param name="indicatorName">Name of the indicator</param>
            <param name="rawValue">Raw indicator value</param>
            <returns>Sell signal</returns>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.ConfidencePercent">
            <summary>
            Gets confidence as percentage (0-100)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.IsTradeable">
            <summary>
            Whether this is a tradeable signal (not neutral)
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.MeetsThreshold(System.Decimal)">
            <summary>
            Whether this signal meets a minimum confidence threshold
            </summary>
            <param name="threshold">Minimum confidence (0.0 to 1.0)</param>
            <returns>True if signal meets threshold</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal.ToString">
            <summary>
            String representation of the signal
            </summary>
            <returns>Formatted signal description</returns>
        </member>
        <member name="T:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle">
            <summary>
            Represents a candle for indicator calculations
            Simplified version of ATAS IndicatorCandle for internal use
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.Open">
            <summary>
            Opening price
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.High">
            <summary>
            Highest price
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.Low">
            <summary>
            Lowest price
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.Close">
            <summary>
            Closing price
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.Volume">
            <summary>
            Volume traded
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.Time">
            <summary>
            Timestamp of the candle
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.TypicalPrice">
            <summary>
            Typical price (HLC/3)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.MedianPrice">
            <summary>
            Median price (HL/2)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.WeightedClose">
            <summary>
            Weighted close price (HLCC/4)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.Range">
            <summary>
            Price range (High - Low)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.IsBullish">
            <summary>
            Whether this is a bullish candle
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.IsBearish">
            <summary>
            Whether this is a bearish candle
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.BodySize">
            <summary>
            Body size of the candle
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.#ctor">
            <summary>
            Creates a new indicator candle
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorCandle.#ctor(System.Decimal,System.Decimal,System.Decimal,System.Decimal,System.Decimal,System.DateTime)">
            <summary>
            Creates a new indicator candle with specified values
            </summary>
        </member>
        <member name="T:HighProbabilityScalpingV2.ATAS.Components.Performance.PerformanceMonitor">
            <summary>
            High-performance monitoring system for tracking operation timings
            Designed to meet less than 20ms processing requirements for Phase 1
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Performance.PerformanceMonitor.#ctor(HighProbabilityScalpingV2.ATAS.Components.Logging.SimpleLogger)">
            <summary>
            Creates a new performance monitor
            </summary>
            <param name="logger">Logger for performance reporting</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Performance.PerformanceMonitor.StartTiming(System.String)">
            <summary>
            Starts timing an operation and returns a disposable scope
            </summary>
            <param name="operation">Name of the operation being timed</param>
            <returns>Disposable timing scope</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Performance.PerformanceMonitor.RecordTiming(System.String,System.TimeSpan)">
            <summary>
            Records a timing measurement for an operation
            </summary>
            <param name="operation">Operation name</param>
            <param name="duration">Duration of the operation</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Performance.PerformanceMonitor.IncrementCounter(System.String)">
            <summary>
            Increments a simple counter
            </summary>
            <param name="counterName">Name of the counter</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Performance.PerformanceMonitor.GetAverageTiming(System.String)">
            <summary>
            Gets average timing for an operation
            </summary>
            <param name="operation">Operation name</param>
            <returns>Average duration, or TimeSpan.Zero if no data</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Performance.PerformanceMonitor.GetMaxTiming(System.String)">
            <summary>
            Gets maximum timing for an operation
            </summary>
            <param name="operation">Operation name</param>
            <returns>Maximum duration, or TimeSpan.Zero if no data</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Performance.PerformanceMonitor.GetCount(System.String)">
            <summary>
            Gets count for an operation or counter
            </summary>
            <param name="name">Operation or counter name</param>
            <returns>Count value</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Performance.PerformanceMonitor.LogPerformanceReport">
            <summary>
            Logs a comprehensive performance report
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Performance.PerformanceMonitor.GetPerformanceStatus(System.String,System.Double)">
            <summary>
            Gets performance status emoji based on timing
            </summary>
            <param name="operation">Operation name</param>
            <param name="averageMs">Average timing in milliseconds</param>
            <returns>Status emoji</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Performance.PerformanceMonitor.LogQuickSummary">
            <summary>
            Logs a quick performance summary
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Performance.PerformanceMonitor.Reset">
            <summary>
            Clears all performance data
            </summary>
        </member>
        <member name="T:HighProbabilityScalpingV2.ATAS.Components.Performance.TimingScope">
            <summary>
            Disposable timing scope for automatic timing measurement
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Performance.TimingScope.#ctor(System.String,HighProbabilityScalpingV2.ATAS.Components.Performance.PerformanceMonitor)">
            <summary>
            Creates a new timing scope
            </summary>
            <param name="operation">Operation being timed</param>
            <param name="monitor">Performance monitor to report to</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Performance.TimingScope.Dispose">
            <summary>
            Completes timing and reports duration
            </summary>
        </member>
        <member name="T:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1">
            <summary>
            High-performance circular buffer for storing fixed-size collections
            Optimized for indicator calculations with minimal memory allocations
            </summary>
            <typeparam name="T">Type of elements to store</typeparam>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1.#ctor(System.Int32)">
            <summary>
            Initializes a new circular buffer with specified capacity
            </summary>
            <param name="capacity">Maximum number of elements to store</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1.Add(`0)">
            <summary>
            Adds an item to the buffer, overwriting oldest item if at capacity
            </summary>
            <param name="item">Item to add</param>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1.Item(System.Int32)">
            <summary>
            Gets element at specified index (0 = oldest, Count-1 = newest)
            </summary>
            <param name="index">Index of element to retrieve</param>
            <returns>Element at specified index</returns>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1.Latest">
            <summary>
            Gets the most recently added element
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1.Count">
            <summary>
            Number of elements currently in the buffer
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1.Capacity">
            <summary>
            Maximum capacity of the buffer
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1.IsFull">
            <summary>
            Whether the buffer is at full capacity
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1.IsEmpty">
            <summary>
            Whether the buffer is empty
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1.ToArray">
            <summary>
            Converts buffer contents to array (oldest to newest)
            </summary>
            <returns>Array containing all elements in chronological order</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1.Sum">
            <summary>
            Calculates sum of numeric elements (for decimal type)
            </summary>
            <returns>Sum of all elements</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1.Average">
            <summary>
            Calculates average of numeric elements (for decimal type)
            </summary>
            <returns>Average of all elements</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1.Clear">
            <summary>
            Clears all elements from the buffer
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.Components.Utils.CircularBuffer`1.GetElements">
            <summary>
            Gets enumerable of all elements (oldest to newest)
            </summary>
            <returns>Enumerable of elements</returns>
        </member>
        <member name="T:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy">
             <summary>
             High-Probability Scalping Strategy v2.0 - Phase 1 Implementation
            
             This is a complete rebuild of the trading strategy focusing on:
             - ATAS-native design without complex dependency injection
             - Single VFI indicator for Phase 1 foundation
             - Less than 5ms performance target per calculation
             - 70% plus confidence signal generation
             - Clean, maintainable architecture for incremental expansion
            
             Phase 1 Success Criteria:
             Strategy loads without errors
             VFI generates signals greater than 70% confidence
             Performance less than 5ms per calculation
             Clear ATAS integration working
             </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.OnStarted">
            <summary>
            Called when strategy starts
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.Dispose">
            <summary>
            Called when strategy stops
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.OnCalculate(System.Int32,System.Decimal)">
            <summary>
            Main calculation method called for each bar
            </summary>
            <param name="bar">Bar index</param>
            <param name="value">Bar value</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.ProcessIndicatorSignal(System.Int32)">
            <summary>
            Processes indicator signal for current bar
            </summary>
            <param name="bar">Current bar index</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.ProcessQualifiedSignal(HighProbabilityScalpingV2.ATAS.Components.Models.IndicatorSignal,System.Int32)">
            <summary>
            Processes qualified signals that meet confidence threshold
            </summary>
            <param name="signal">Qualified signal</param>
            <param name="bar">Current bar</param>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.GetIndicatorCandle(System.Int32)">
            <summary>
            Converts ATAS candle to internal candle format
            </summary>
            <param name="bar">Bar index</param>
            <returns>Internal candle representation</returns>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.InitializeComponents">
            <summary>
            Initializes all strategy components
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.ValidateConfiguration">
            <summary>
            Validates strategy configuration
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.LogStartupInfo">
            <summary>
            Logs strategy startup information
            </summary>
        </member>
        <member name="M:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.LogShutdownInfo">
            <summary>
            Logs strategy shutdown information
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.CurrentVFIValue">
            <summary>
            Current VFI value for external monitoring
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.IsVFIReady">
            <summary>
            Whether VFI indicator is ready
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.BarsProcessed">
            <summary>
            Total number of bars processed
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.SignalsGenerated">
            <summary>
            Total signals generated
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.QualifiedSignals">
            <summary>
            Qualified signals (meeting confidence threshold)
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.SignalQualificationRate">
            <summary>
            Signal qualification rate as percentage
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.AverageVFICalculationTime">
            <summary>
            Average VFI calculation time in milliseconds
            </summary>
        </member>
        <member name="P:HighProbabilityScalpingV2.ATAS.HighProbabilityScalpingV2Strategy.StrategyStatus">
            <summary>
            Strategy status for debugging
            </summary>
        </member>
    </members>
</doc>
