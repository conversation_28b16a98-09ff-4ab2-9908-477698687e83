using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using ATAS.Indicators;
using ATAS.Strategies.Chart;
using HighProbabilityScalpingV2.ATAS.Components.Indicators;
using HighProbabilityScalpingV2.ATAS.Components.Logging;
using HighProbabilityScalpingV2.ATAS.Components.Models;
using HighProbabilityScalpingV2.ATAS.Components.Performance;

namespace HighProbabilityScalpingV2.ATAS
{
    /// <summary>
    /// High-Probability Scalping Strategy v2.0 - Phase 1 Implementation
    ///
    /// This is a complete rebuild of the trading strategy focusing on:
    /// - ATAS-native design without complex dependency injection
    /// - Single VFI indicator for Phase 1 foundation
    /// - Less than 5ms performance target per calculation
    /// - 70% plus confidence signal generation
    /// - Clean, maintainable architecture for incremental expansion
    ///
    /// Phase 1 Success Criteria:
    /// Strategy loads without errors
    /// VFI generates signals greater than 70% confidence
    /// Performance less than 5ms per calculation
    /// Clear ATAS integration working
    /// </summary>
    [Display(Name = "High-Probability Scalping v2.0")]
    public class HighProbabilityScalpingV2Strategy : ChartStrategy
    {
        #region User Settings

        [Display(Name = "VFI Period", GroupName = "VFI Settings", Order = 1)]
        [Range(5, 50)]
        public int VFIPeriod { get; set; } = 14;

        [Display(Name = "VFI Buy Threshold", GroupName = "VFI Settings", Order = 2)]
        [Range(0.5, 3.0)]
        public decimal VFIBuyThreshold { get; set; } = 1.3m;

        [Display(Name = "VFI Sell Threshold", GroupName = "VFI Settings", Order = 3)]
        [Range(0.1, 1.5)]
        public decimal VFISellThreshold { get; set; } = 0.7m;

        [Display(Name = "Min Confidence %", GroupName = "Signal Settings", Order = 10)]
        [Range(60, 95)]
        public int MinConfidence { get; set; } = 70;

        [Display(Name = "Enable Debug Logging", GroupName = "Debug", Order = 20)]
        public bool EnableDebugLogging { get; set; } = true;

        [Display(Name = "Performance Reporting Interval", GroupName = "Debug", Order = 21)]
        [Range(50, 500)]
        public int PerformanceReportInterval { get; set; } = 100;

        #endregion

        #region Private Fields

        private VolumeFlowIndex? _vfi;
        private SimpleLogger? _logger;
        private PerformanceMonitor? _performance;
        private int _barCount;
        private int _signalCount;
        private int _qualifiedSignalCount;

        #endregion

        #region Strategy Lifecycle

        /// <summary>
        /// Called when strategy starts
        /// </summary>
        protected override void OnStarted()
        {
            try
            {
                InitializeComponents();
                ValidateConfiguration();
                LogStartupInfo();
                
                _logger?.LogInfo("✅ High-Probability Scalping v2.0 started successfully");
            }
            catch (Exception ex)
            {
                var errorMsg = $"❌ Failed to start strategy: {ex.Message}";
                _logger?.LogError(errorMsg, ex);
                
                // Also try to show in ATAS (may not work if logger failed)
                try
                {
                    RaiseShowNotification(errorMsg, "Strategy Error");
                }
                catch
                {
                    // Ignore if ATAS logging also fails
                }
                
                throw;
            }
        }

        /// <summary>
        /// Called when strategy stops
        /// </summary>
        public override void Dispose()
        {
            try
            {
                LogShutdownInfo();
                _logger?.LogShutdown();
            }
            catch (Exception ex)
            {
                // Don't throw during disposal
                try
                {
                    _logger?.LogError("Error during strategy disposal", ex);
                }
                catch
                {
                    // Ignore if logging fails during disposal
                }
            }

            base.Dispose();
        }

        /// <summary>
        /// Main calculation method called for each bar
        /// </summary>
        /// <param name="bar">Bar index</param>
        /// <param name="value">Bar value</param>
        protected override void OnCalculate(int bar, decimal value)
        {
            try
            {
                // Skip if not enough data
                if (bar < VFIPeriod)
                {
                    return;
                }

                _barCount++;

                // Process indicator signal with performance monitoring
                using (_performance?.StartTiming("TotalCalculation"))
                {
                    ProcessIndicatorSignal(bar);
                }

                // Periodic performance reporting
                if (_barCount % PerformanceReportInterval == 0)
                {
                    _performance?.LogQuickSummary();
                }

                // Full performance report every 500 bars
                if (_barCount % 500 == 0)
                {
                    _performance?.LogPerformanceReport();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error in OnCalculate for bar {bar}", ex);
            }
        }

        #endregion

        #region Core Processing

        /// <summary>
        /// Processes indicator signal for current bar
        /// </summary>
        /// <param name="bar">Current bar index</param>
        private void ProcessIndicatorSignal(int bar)
        {
            try
            {
                IndicatorSignal signal;

                // Calculate VFI signal with performance monitoring
                using (_performance?.StartTiming("VFI_Calculation"))
                {
                    var candle = GetIndicatorCandle(bar);
                    signal = _vfi!.Calculate(candle);
                }

                _signalCount++;

                // Process signal
                if (signal.Direction != SignalDirection.Neutral)
                {
                    var confidencePercent = signal.ConfidencePercent;

                    _logger?.LogSignal($"📊 VFI Signal: {signal.Direction} @ {confidencePercent:F1}% - {signal.Reason}");

                    // Check if signal meets confidence threshold
                    if (confidencePercent >= MinConfidence)
                    {
                        _qualifiedSignalCount++;
                        _logger?.LogSignal($"✅ Signal QUALIFIED: {signal.Direction} @ {confidencePercent:F1}% (threshold: {MinConfidence}%)");

                        // Future: Add trade execution logic here in later phases
                        ProcessQualifiedSignal(signal, bar);
                    }
                    else
                    {
                        _logger?.LogDebug($"⚠️ Signal below threshold: {confidencePercent:F1}% < {MinConfidence}%");
                    }
                }
                else
                {
                    _logger?.LogDebug($"➡️ Neutral signal: {signal.Reason}");
                }

                // Update performance counters
                _performance?.IncrementCounter("TotalSignals");
                if (signal.Direction != SignalDirection.Neutral)
                {
                    _performance?.IncrementCounter("NonNeutralSignals");
                }
                if (signal.ConfidencePercent >= MinConfidence)
                {
                    _performance?.IncrementCounter("QualifiedSignals");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error processing indicator signal for bar {bar}", ex);
            }
        }

        /// <summary>
        /// Processes qualified signals that meet confidence threshold
        /// </summary>
        /// <param name="signal">Qualified signal</param>
        /// <param name="bar">Current bar</param>
        private void ProcessQualifiedSignal(IndicatorSignal signal, int bar)
        {
            try
            {
                // Phase 1: Just log qualified signals
                // Future phases will add actual trade execution

                _logger?.LogTrade($"🎯 QUALIFIED SIGNAL: {signal.Direction} @ {signal.ConfidencePercent:F1}% on bar {bar}");
                _logger?.LogTrade($"   📈 VFI Value: {signal.RawValue:F3}");
                _logger?.LogTrade($"   ⏰ Time: {DateTime.Now:HH:mm:ss}");

                // Show notification in ATAS (if possible)
                try
                {
                    var message = $"VFI {signal.Direction} @ {signal.ConfidencePercent:F1}%";
                    RaiseShowNotification(message);
                }
                catch
                {
                    // Ignore if ATAS notification fails
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error processing qualified signal", ex);
            }
        }

        /// <summary>
        /// Converts ATAS candle to internal candle format
        /// </summary>
        /// <param name="bar">Bar index</param>
        /// <returns>Internal candle representation</returns>
        private Components.Models.IndicatorCandle GetIndicatorCandle(int bar)
        {
            var atasCandle = GetCandle(bar);
            return new Components.Models.IndicatorCandle
            {
                Open = (decimal)atasCandle.Open,
                High = (decimal)atasCandle.High,
                Low = (decimal)atasCandle.Low,
                Close = (decimal)atasCandle.Close,
                Volume = (decimal)atasCandle.Volume,
                Time = atasCandle.Time
            };
        }

        #endregion

        #region Initialization and Configuration

        /// <summary>
        /// Initializes all strategy components
        /// </summary>
        private void InitializeComponents()
        {
            // Initialize logger first
            _logger = new SimpleLogger("HighProbabilityScalpingV2", EnableDebugLogging);

            // Initialize performance monitor
            _performance = new PerformanceMonitor(_logger);

            // Initialize VFI indicator
            _vfi = new VolumeFlowIndex(VFIPeriod, VFIBuyThreshold, VFISellThreshold);

            // Reset counters
            _barCount = 0;
            _signalCount = 0;
            _qualifiedSignalCount = 0;

            _logger.LogInfo("✅ All components initialized successfully");
            _logger.LogInfo($"📊 VFI Settings: Period={VFIPeriod}, Buy={VFIBuyThreshold}, Sell={VFISellThreshold}");
            _logger.LogInfo($"🎯 Min Confidence: {MinConfidence}%");
            _logger.LogInfo($"📈 Performance Reporting: Every {PerformanceReportInterval} bars");
        }

        /// <summary>
        /// Validates strategy configuration
        /// </summary>
        private void ValidateConfiguration()
        {
            var errors = new List<string>();

            // Validate VFI settings
            if (VFIPeriod < 5 || VFIPeriod > 50)
                errors.Add($"VFI Period {VFIPeriod} outside valid range (5-50)");

            if (VFIBuyThreshold < 0.5m || VFIBuyThreshold > 3.0m)
                errors.Add($"VFI Buy Threshold {VFIBuyThreshold} outside valid range (0.5-3.0)");

            if (VFISellThreshold < 0.1m || VFISellThreshold > 1.5m)
                errors.Add($"VFI Sell Threshold {VFISellThreshold} outside valid range (0.1-1.5)");

            // Validate signal settings
            if (MinConfidence < 60 || MinConfidence > 95)
                errors.Add($"Min Confidence {MinConfidence} outside valid range (60-95)");

            // Validate performance settings
            if (PerformanceReportInterval < 50 || PerformanceReportInterval > 500)
                errors.Add($"Performance Report Interval {PerformanceReportInterval} outside valid range (50-500)");

            if (errors.Count > 0)
            {
                var errorMsg = "Configuration errors: " + string.Join(", ", errors);
                _logger?.LogError(errorMsg);
                throw new InvalidOperationException(errorMsg);
            }

            _logger?.LogInfo("✅ Configuration validation passed");
        }

        #endregion

        #region Logging and Reporting

        /// <summary>
        /// Logs strategy startup information
        /// </summary>
        private void LogStartupInfo()
        {
            _logger?.LogStartup("v2.0.0", "Phase 1");
            _logger?.LogInfo($"🔧 ATAS Integration: {(Container != null ? "Connected" : "Disconnected")}");
            _logger?.LogInfo($"📊 Instrument: {(InstrumentInfo?.Instrument ?? "Unknown")}");
            _logger?.LogInfo($"⏰ Timeframe: {ChartInfo?.ChartType ?? "Unknown"}");
        }

        /// <summary>
        /// Logs strategy shutdown information
        /// </summary>
        private void LogShutdownInfo()
        {
            _logger?.LogSeparator("FINAL STATISTICS");
            _logger?.LogInfo($"📊 Total Bars Processed: {_barCount:N0}");
            _logger?.LogInfo($"📈 Total Signals Generated: {_signalCount:N0}");
            _logger?.LogInfo($"✅ Qualified Signals: {_qualifiedSignalCount:N0}");

            if (_signalCount > 0)
            {
                var qualificationRate = (decimal)_qualifiedSignalCount / _signalCount * 100;
                _logger?.LogInfo($"🎯 Signal Qualification Rate: {qualificationRate:F1}%");
            }

            if (_vfi != null)
            {
                _logger?.LogInfo($"⚡ VFI Performance: {_vfi.LastCalculationTime.TotalMilliseconds:F2}ms avg");
                _logger?.LogInfo($"🔢 VFI Calculations: {_vfi.CalculationCount:N0}");
            }

            _performance?.LogPerformanceReport();
        }

        #endregion

        #region Public Properties for Monitoring

        /// <summary>
        /// Current VFI value for external monitoring
        /// </summary>
        public decimal CurrentVFIValue => _vfi?.CurrentValue ?? 0;

        /// <summary>
        /// Whether VFI indicator is ready
        /// </summary>
        public bool IsVFIReady => _vfi?.IsReady ?? false;

        /// <summary>
        /// Total number of bars processed
        /// </summary>
        public int BarsProcessed => _barCount;

        /// <summary>
        /// Total signals generated
        /// </summary>
        public int SignalsGenerated => _signalCount;

        /// <summary>
        /// Qualified signals (meeting confidence threshold)
        /// </summary>
        public int QualifiedSignals => _qualifiedSignalCount;

        /// <summary>
        /// Signal qualification rate as percentage
        /// </summary>
        public decimal SignalQualificationRate =>
            _signalCount > 0 ? (decimal)_qualifiedSignalCount / _signalCount * 100 : 0;

        /// <summary>
        /// Average VFI calculation time in milliseconds
        /// </summary>
        public double AverageVFICalculationTime =>
            _vfi?.LastCalculationTime.TotalMilliseconds ?? 0;

        /// <summary>
        /// Strategy status for debugging
        /// </summary>
        public string StrategyStatus =>
            $"Bars: {_barCount}, Signals: {_signalCount}, Qualified: {_qualifiedSignalCount}, " +
            $"VFI: {(_vfi?.IsReady == true ? "Ready" : "Not Ready")}";

        #endregion
    }
}
