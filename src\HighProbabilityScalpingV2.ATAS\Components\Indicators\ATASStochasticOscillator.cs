using ATAS.Indicators;
using ATAS.Indicators.Drawing;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace HighProbabilityScalpingV2.ATAS.Components.Indicators
{
    /// <summary>
    /// ATAS-Compatible Stochastic Oscillator Indicator
    /// Wraps our custom Stochastic logic in ATAS indicator pattern
    /// </summary>
    [DisplayName("Stochastic Oscillator")]
    public class ATASStochasticOscillator : Indicator
    {
        #region Fields
        private readonly ValueDataSeries _kSeries;
        private readonly ValueDataSeries _dSeries;
        private readonly ValueDataSeries _signalSeries;
        
        // Calculation fields
        private int _kPeriod = 14;
        private int _dPeriod = 3;
        private decimal _overboughtThreshold = 80m;
        private decimal _oversoldThreshold = 20m;
        
        // Data storage
        private readonly CircularBuffer<decimal> _highs;
        private readonly CircularBuffer<decimal> _lows;
        private readonly CircularBuffer<decimal> _closes;
        private readonly CircularBuffer<decimal> _kValues;
        
        private decimal _lastK = 50m;
        private decimal _lastD = 50m;
        #endregion

        #region Properties
        [Display(Name = "K Period", Order = 10)]
        public int KPeriod
        {
            get => _kPeriod;
            set
            {
                _kPeriod = Math.Max(1, value);
                RecalculateValues();
            }
        }

        [Display(Name = "D Period", Order = 20)]
        public int DPeriod
        {
            get => _dPeriod;
            set
            {
                _dPeriod = Math.Max(1, value);
                RecalculateValues();
            }
        }

        [Display(Name = "Overbought", Order = 30)]
        public decimal OverboughtThreshold
        {
            get => _overboughtThreshold;
            set
            {
                _overboughtThreshold = value;
                RecalculateValues();
            }
        }

        [Display(Name = "Oversold", Order = 40)]
        public decimal OversoldThreshold
        {
            get => _oversoldThreshold;
            set
            {
                _oversoldThreshold = value;
                RecalculateValues();
            }
        }
        #endregion

        #region Constructor
        public ATASStochasticOscillator()
            : base(true)
        {
            // Initialize data series for visualization
            _kSeries = (ValueDataSeries)DataSeries[0];
            _kSeries.Name = "%K";
            _kSeries.Color = System.Drawing.Color.Blue;
            _kSeries.VisualType = VisualMode.Line;

            _dSeries = new ValueDataSeries("%D")
            {
                Color = System.Drawing.Color.Red,
                VisualType = VisualMode.Line
            };
            DataSeries.Add(_dSeries);

            _signalSeries = new ValueDataSeries("Signal")
            {
                Color = System.Drawing.Color.Green,
                VisualType = VisualMode.Histogram
            };
            DataSeries.Add(_signalSeries);

            // Initialize circular buffers
            _highs = new CircularBuffer<decimal>(_kPeriod);
            _lows = new CircularBuffer<decimal>(_kPeriod);
            _closes = new CircularBuffer<decimal>(_kPeriod);
            _kValues = new CircularBuffer<decimal>(_dPeriod);
        }
        #endregion

        #region ATAS Indicator Implementation
        protected override void OnCalculate(int bar, decimal value)
        {
            // Get candle data
            var candle = GetCandle(bar);
            if (candle == null) return;

            // Add OHLC data to buffers
            _highs.Add(candle.High);
            _lows.Add(candle.Low);
            _closes.Add(candle.Close);

            // Calculate %K if we have enough data
            if (_highs.Count >= _kPeriod)
            {
                // Find highest high and lowest low over K period
                var highestHigh = decimal.MinValue;
                var lowestLow = decimal.MaxValue;

                for (int i = 0; i < _kPeriod; i++)
                {
                    var high = _highs[_highs.Count - 1 - i];
                    var low = _lows[_lows.Count - 1 - i];
                    
                    if (high > highestHigh) highestHigh = high;
                    if (low < lowestLow) lowestLow = low;
                }

                // Calculate %K
                var currentClose = _closes[_closes.Count - 1];
                if (highestHigh != lowestLow)
                {
                    _lastK = ((currentClose - lowestLow) / (highestHigh - lowestLow)) * 100m;
                }
                else
                {
                    _lastK = 50m; // Neutral when no range
                }

                _kValues.Add(_lastK);

                // Calculate %D (smoothed %K) if we have enough K values
                if (_kValues.Count >= _dPeriod)
                {
                    var kSum = 0m;
                    for (int i = 0; i < _dPeriod; i++)
                    {
                        kSum += _kValues[_kValues.Count - 1 - i];
                    }
                    _lastD = kSum / _dPeriod;

                    // Set values for visualization
                    _kSeries[bar] = _lastK;
                    _dSeries[bar] = _lastD;

                    // Generate signal based on crossovers and levels
                    var signal = 0m;
                    
                    // Overbought/Oversold signals
                    if (_lastK >= _overboughtThreshold && _lastD >= _overboughtThreshold)
                        signal = -1m; // Strong sell signal
                    else if (_lastK <= _oversoldThreshold && _lastD <= _oversoldThreshold)
                        signal = 1m;  // Strong buy signal
                    else if (_lastK > _lastD && _lastK > 50m)
                        signal = 0.5m; // Weak buy signal (K above D and above midline)
                    else if (_lastK < _lastD && _lastK < 50m)
                        signal = -0.5m; // Weak sell signal (K below D and below midline)

                    _signalSeries[bar] = signal;
                }
            }
        }

        /// <summary>
        /// Gets the %K value for a specific bar
        /// </summary>
        public decimal GetK(int bar)
        {
            return _kSeries[bar];
        }

        /// <summary>
        /// Gets the %D value for a specific bar
        /// </summary>
        public decimal GetD(int bar)
        {
            return _dSeries[bar];
        }

        /// <summary>
        /// Gets the current K and D values
        /// </summary>
        public (decimal K, decimal D) CurrentValues => (_lastK, _lastD);

        /// <summary>
        /// Checks if indicator is ready (has enough data)
        /// </summary>
        public bool IsReady => _kValues.Count >= _dPeriod;
        #endregion
    }
}
