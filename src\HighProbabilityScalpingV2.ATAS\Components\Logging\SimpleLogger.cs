using System;
using System.IO;

namespace HighProbabilityScalpingV2.ATAS.Components.Logging
{
    /// <summary>
    /// Simple, high-performance logging system for ATAS strategies
    /// Avoids complex dependencies and provides thread-safe file logging
    /// </summary>
    public class SimpleLogger
    {
        private readonly string _logPath;
        private readonly bool _enableDebug;
        private readonly object _lockObject = new object();
        private readonly string _strategyName;

        /// <summary>
        /// Creates a new simple logger instance
        /// </summary>
        /// <param name="strategyName">Name of the strategy for log identification</param>
        /// <param name="enableDebug">Whether to enable debug-level logging</param>
        public SimpleLogger(string strategyName, bool enableDebug = true)
        {
            _strategyName = strategyName ?? "UnknownStrategy";
            _enableDebug = enableDebug;
            
            // Create log file with timestamp
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var logFileName = $"{_strategyName}_v2.0_{timestamp}.log";
            
            // Use Desktop/Smart Trading folder for logs
            var logDirectory = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.Desktop),
                "Smart Trading",
                "ATAS_Strategy_Logs"
            );
            
            Directory.CreateDirectory(logDirectory);
            _logPath = Path.Combine(logDirectory, logFileName);
            
            // Write startup header
            LogInfo("=".PadRight(60, '='));
            LogInfo($"🚀 {_strategyName} v2.0 LOG STARTED");
            LogInfo($"📅 {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            LogInfo($"💻 Machine: {Environment.MachineName}");
            LogInfo($"📁 Log File: {_logPath}");
            LogInfo("=".PadRight(60, '='));
        }

        /// <summary>
        /// Logs an informational message
        /// </summary>
        /// <param name="message">Message to log</param>
        public void LogInfo(string message)
        {
            WriteLog("INFO", message);
        }

        /// <summary>
        /// Logs a warning message
        /// </summary>
        /// <param name="message">Warning message to log</param>
        public void LogWarning(string message)
        {
            WriteLog("WARNING", message);
        }

        /// <summary>
        /// Logs an error message
        /// </summary>
        /// <param name="message">Error message to log</param>
        public void LogError(string message)
        {
            WriteLog("ERROR", message);
        }

        /// <summary>
        /// Logs an error with exception details
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="exception">Exception to log</param>
        public void LogError(string message, Exception exception)
        {
            var fullMessage = $"{message} | Exception: {exception.GetType().Name}: {exception.Message}";
            if (exception.StackTrace != null)
            {
                fullMessage += $" | Stack: {exception.StackTrace.Split('\n')[0].Trim()}";
            }
            WriteLog("ERROR", fullMessage);
        }

        /// <summary>
        /// Logs a debug message (only if debug logging is enabled)
        /// </summary>
        /// <param name="message">Debug message to log</param>
        public void LogDebug(string message)
        {
            if (_enableDebug)
            {
                WriteLog("DEBUG", message);
            }
        }

        /// <summary>
        /// Logs a signal-related message with special formatting
        /// </summary>
        /// <param name="message">Signal message to log</param>
        public void LogSignal(string message)
        {
            WriteLog("SIGNAL", message);
        }

        /// <summary>
        /// Logs a performance-related message
        /// </summary>
        /// <param name="message">Performance message to log</param>
        public void LogPerformance(string message)
        {
            WriteLog("PERF", message);
        }

        /// <summary>
        /// Logs a trade-related message
        /// </summary>
        /// <param name="message">Trade message to log</param>
        public void LogTrade(string message)
        {
            WriteLog("TRADE", message);
        }

        /// <summary>
        /// Core logging method that writes to file
        /// </summary>
        /// <param name="level">Log level</param>
        /// <param name="message">Message to log</param>
        private void WriteLog(string level, string message)
        {
            try
            {
                lock (_lockObject)
                {
                    var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
                    var logEntry = $"[{timestamp}] [{level,-7}] {message}";
                    
                    // Write to file
                    File.AppendAllText(_logPath, logEntry + Environment.NewLine);
                    
                    // Also write to console for debugging (if available)
                    Console.WriteLine(logEntry);
                }
            }
            catch (Exception ex)
            {
                // Fallback - write to console if file logging fails
                Console.WriteLine($"[LOGGER ERROR] Failed to write log: {ex.Message}");
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] [{level,-7}] {message}");
            }
        }

        /// <summary>
        /// Logs a separator line for visual organization
        /// </summary>
        /// <param name="title">Optional title for the separator</param>
        public void LogSeparator(string title = "")
        {
            if (string.IsNullOrEmpty(title))
            {
                LogInfo("-".PadRight(50, '-'));
            }
            else
            {
                var padding = Math.Max(0, (50 - title.Length - 2) / 2);
                var separator = "-".PadRight(padding, '-') + $" {title} " + "-".PadRight(padding, '-');
                LogInfo(separator);
            }
        }

        /// <summary>
        /// Logs strategy startup information
        /// </summary>
        /// <param name="version">Strategy version</param>
        /// <param name="phase">Current phase</param>
        public void LogStartup(string version, string phase)
        {
            LogSeparator("STARTUP");
            LogInfo($"🎯 Strategy: {_strategyName}");
            LogInfo($"📊 Version: {version}");
            LogInfo($"🔄 Phase: {phase}");
            LogInfo($"⚡ Debug Logging: {(_enableDebug ? "Enabled" : "Disabled")}");
            LogSeparator();
        }

        /// <summary>
        /// Logs strategy shutdown information
        /// </summary>
        public void LogShutdown()
        {
            LogSeparator("SHUTDOWN");
            LogInfo($"🛑 {_strategyName} v2.0 STOPPED");
            LogInfo($"📅 {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            LogSeparator();
        }

        /// <summary>
        /// Gets the current log file path
        /// </summary>
        public string LogFilePath => _logPath;

        /// <summary>
        /// Whether debug logging is enabled
        /// </summary>
        public bool IsDebugEnabled => _enableDebug;
    }
}
