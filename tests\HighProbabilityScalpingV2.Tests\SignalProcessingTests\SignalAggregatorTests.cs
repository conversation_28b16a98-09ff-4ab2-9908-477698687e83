using System;
using System.Linq;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using HighProbabilityScalpingV2.ATAS.Components.Indicators;
using HighProbabilityScalpingV2.ATAS.Components.Interfaces;
using HighProbabilityScalpingV2.ATAS.Components.Logging;
using HighProbabilityScalpingV2.ATAS.Components.Models;
using HighProbabilityScalpingV2.ATAS.Components.SignalProcessing;

namespace HighProbabilityScalpingV2.Tests.SignalProcessingTests
{
    /// <summary>
    /// Unit tests for SignalAggregator
    /// Validates Phase 2 success criteria:
    /// - Weighted signal aggregation works correctly
    /// - Minimum indicator alignment enforced
    /// - Confidence thresholds properly applied
    /// - Performance <20ms per aggregation
    /// </summary>
    [TestClass]
    public class SignalAggregatorTests
    {
        private SignalAggregator _aggregator = null!;
        private SimpleLogger _logger = null!;
        private VolumeFlowIndex _vfi = null!;
        private UltraFastRSI _rsi = null!;

        [TestInitialize]
        public void Setup()
        {
            _logger = new SimpleLogger("TestAggregator", false); // Disable debug for tests
            _aggregator = new SignalAggregator(_logger, minimumConfidence: 0.8m, minimumIndicatorAlignment: 2);
            
            // Create test indicators
            _vfi = new VolumeFlowIndex(period: 3, buyThreshold: 0.5m, sellThreshold: 0.5m);
            _rsi = new UltraFastRSI(period: 3, overboughtLevel: 70, oversoldLevel: 30);
        }

        [TestMethod]
        public void Aggregator_WithNoIndicators_ReturnsNeutral()
        {
            // Arrange
            var candle = CreateTestCandle(100, 1000);
            var marketData = new MarketData();

            // Act
            var signal = _aggregator.AggregateSignals(candle, marketData);

            // Assert
            Assert.AreEqual(SignalDirection.Neutral, signal.Direction);
            Assert.AreEqual(0, signal.Confidence);
            Assert.IsTrue(signal.Reason.Contains("No enabled indicators"));
        }

        [TestMethod]
        public void Aggregator_AddRemoveIndicators_WorksCorrectly()
        {
            // Act - Add indicators
            _aggregator.AddIndicator(_vfi);
            _aggregator.AddIndicator(_rsi);

            // Assert
            Assert.AreEqual(2, _aggregator.GetIndicators().Count);
            Assert.IsNotNull(_aggregator.GetIndicator("VFI"));
            Assert.IsNotNull(_aggregator.GetIndicator("UltraFastRSI"));

            // Act - Remove indicator
            var removed = _aggregator.RemoveIndicator("VFI");

            // Assert
            Assert.IsTrue(removed);
            Assert.AreEqual(1, _aggregator.GetIndicators().Count);
            Assert.IsNull(_aggregator.GetIndicator("VFI"));
        }

        [TestMethod]
        public void Aggregator_WithInsufficientAlignment_ReturnsNeutral()
        {
            // Arrange - Add indicators but set high alignment requirement
            var aggregator = new SignalAggregator(_logger, minimumConfidence: 0.7m, minimumIndicatorAlignment: 3);
            aggregator.AddIndicator(_vfi);
            aggregator.AddIndicator(_rsi);

            // Prepare indicators with data
            PrepareIndicatorsWithData();

            var candle = CreateTestCandle(100, 1000);
            var marketData = new MarketData();

            // Act
            var signal = aggregator.AggregateSignals(candle, marketData);

            // Assert - Should be neutral due to insufficient alignment (need 3, have max 2)
            Assert.AreEqual(SignalDirection.Neutral, signal.Direction);
            Assert.IsTrue(signal.Reason.Contains("Insufficient alignment"));
        }

        [TestMethod]
        public void Aggregator_WithSufficientAlignment_GeneratesSignal()
        {
            // Arrange
            _aggregator.AddIndicator(_vfi);
            _aggregator.AddIndicator(_rsi);

            // Prepare indicators with data to make them ready
            PrepareIndicatorsWithData();

            var candle = CreateTestCandle(120, 1000); // Price that should generate signals
            var marketData = new MarketData();

            // Act
            var signal = _aggregator.AggregateSignals(candle, marketData);

            // Assert
            Assert.IsNotNull(signal);
            Assert.AreEqual("SignalAggregator", signal.IndicatorName);
            
            // Check metadata
            Assert.IsTrue(signal.Metadata.ContainsKey("TotalIndicators"));
            Assert.IsTrue(signal.Metadata.ContainsKey("EnabledIndicators"));
            Assert.IsTrue(signal.Metadata.ContainsKey("ReadyIndicators"));
        }

        [TestMethod]
        public void Aggregator_WeightedVoting_WorksCorrectly()
        {
            // Arrange - Create mock indicators with known weights
            var mockIndicator1 = new MockIndicator("Mock1", 0.6m); // 60% weight
            var mockIndicator2 = new MockIndicator("Mock2", 0.4m); // 40% weight

            _aggregator.AddIndicator(mockIndicator1);
            _aggregator.AddIndicator(mockIndicator2);

            // Set up mock indicators to return specific signals
            mockIndicator1.SetNextSignal(SignalDirection.Buy, 0.8m); // 60% * 80% = 48%
            mockIndicator2.SetNextSignal(SignalDirection.Buy, 0.9m); // 40% * 90% = 36%
            // Expected weighted average: (48% + 36%) / (60% + 40%) = 84%

            var candle = CreateTestCandle(100, 1000);
            var marketData = new MarketData();

            // Act
            var signal = _aggregator.AggregateSignals(candle, marketData);

            // Assert
            Assert.AreEqual(SignalDirection.Buy, signal.Direction);
            Assert.IsTrue(signal.Confidence >= 0.8m, $"Expected confidence >= 80%, got {signal.ConfidencePercent:F1}%");
        }

        [TestMethod]
        public void Aggregator_PerformanceTest_MeetsTargetTime()
        {
            // Arrange
            _aggregator.AddIndicator(_vfi);
            _aggregator.AddIndicator(_rsi);
            PrepareIndicatorsWithData();

            var candle = CreateTestCandle(100, 1000);
            var marketData = new MarketData();

            // Act - Measure performance
            var startTime = DateTime.UtcNow;
            _aggregator.AggregateSignals(candle, marketData);
            var duration = DateTime.UtcNow - startTime;

            // Assert - Should meet <20ms target for aggregation
            Assert.IsTrue(duration.TotalMilliseconds < 20, 
                $"Signal aggregation took {duration.TotalMilliseconds:F2}ms, target: <20ms");
        }

        [TestMethod]
        public void Aggregator_Statistics_TrackCorrectly()
        {
            // Arrange
            _aggregator.AddIndicator(_vfi);
            PrepareIndicatorsWithData();

            // Act - Generate several signals
            for (int i = 0; i < 5; i++)
            {
                var candle = CreateTestCandle(100 + i * 5, 1000);
                _aggregator.AggregateSignals(candle, new MarketData());
            }

            // Assert
            Assert.IsTrue(_aggregator.AggregationCount >= 5);
            
            var stats = _aggregator.GetStatistics();
            Assert.IsNotNull(stats);
            Assert.IsTrue(stats.Contains("Aggregations"));
            Assert.IsTrue(stats.Contains("Signals"));
        }

        [TestMethod]
        public void Aggregator_ConflictingSignals_HandledCorrectly()
        {
            // Arrange - Create indicators that will give conflicting signals
            var mockBuy = new MockIndicator("MockBuy", 0.5m);
            var mockSell = new MockIndicator("MockSell", 0.5m);

            _aggregator.AddIndicator(mockBuy);
            _aggregator.AddIndicator(mockSell);

            // Set conflicting signals
            mockBuy.SetNextSignal(SignalDirection.Buy, 0.9m);
            mockSell.SetNextSignal(SignalDirection.Sell, 0.9m);

            var candle = CreateTestCandle(100, 1000);
            var marketData = new MarketData();

            // Act
            var signal = _aggregator.AggregateSignals(candle, marketData);

            // Assert - Should be neutral due to conflicting signals
            Assert.AreEqual(SignalDirection.Neutral, signal.Direction);
            Assert.IsTrue(signal.Reason.Contains("Insufficient alignment"));
        }

        /// <summary>
        /// Prepares indicators with enough data to make them ready
        /// </summary>
        private void PrepareIndicatorsWithData()
        {
            // Add data to make indicators ready
            for (int i = 0; i < 5; i++)
            {
                var candle = CreateTestCandle(100 + i, 1000);
                var marketData = new MarketData();
                
                _vfi.Calculate(candle, marketData);
                _rsi.Calculate(candle, marketData);
            }
        }

        /// <summary>
        /// Helper method to create test candles
        /// </summary>
        private IndicatorCandle CreateTestCandle(decimal price, decimal volume, DateTime? time = null)
        {
            return new IndicatorCandle
            {
                Open = price - 0.5m,
                High = price + 1,
                Low = price - 1,
                Close = price,
                Volume = volume,
                Time = time ?? DateTime.UtcNow
            };
        }
    }

    /// <summary>
    /// Mock indicator for testing signal aggregation
    /// </summary>
    public class MockIndicator : IIndicator
    {
        private SignalDirection _nextDirection = SignalDirection.Neutral;
        private decimal _nextConfidence = 0;
        private readonly string _name;
        private decimal _weight;

        public MockIndicator(string name, decimal weight)
        {
            _name = name;
            _weight = weight;
        }

        public void SetNextSignal(SignalDirection direction, decimal confidence)
        {
            _nextDirection = direction;
            _nextConfidence = confidence;
        }

        public string Name => _name;
        public string Category => "Mock";
        public decimal Weight { get => _weight; set => _weight = value; }
        public bool IsEnabled { get; set; } = true;
        public bool IsReady => true;
        public TimeSpan LastCalculationTime => TimeSpan.FromMilliseconds(1);

        public IndicatorSignal Calculate(IndicatorCandle candle, MarketData marketData)
        {
            return new IndicatorSignal(_nextDirection, _nextConfidence, "Mock signal", _name);
        }

        public void Reset() { }

        public IndicatorStatus GetStatus()
        {
            return new IndicatorStatus
            {
                IsOperational = true,
                StatusMessage = "Mock indicator ready"
            };
        }
    }
}
