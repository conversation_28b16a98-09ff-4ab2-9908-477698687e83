# Phase 2: Multi-Indicator Implementation
# High-Probability Scalping Strategy v2.0

**Phase Duration:** Week 2 (7 days)
**Status:** Planning
**Priority:** Core Functionality
**Dependencies:** Phase 1 Complete

---

## 🎯 **PHASE 2 OBJECTIVES**

Expand the foundation from Phase 1 to include all 5 Phase 1 indicators and implement the IndicatorManager system that demonstrates:
- All 5 traditional indicators working simultaneously
- IndicatorManager coordinating multiple indicators
- Signal aggregation from multiple sources
- Performance optimization for multiple calculations
- Enhanced UI with individual indicator controls

### **Success Criteria:**
- ✅ All 5 Phase 1 indicators operational (VFI, RSI, Bollinger Bands, Stochastic, MACD)
- ✅ Signal logs show "Phase1: 3-5" contributions instead of "Phase1: 0"
- ✅ Combined confidence >80% for quality signals
- ✅ Performance <15ms total for all indicators
- ✅ Individual indicator enable/disable functionality

---

## 📋 **DETAILED TASK BREAKDOWN**

### **Day 1: Indicator Architecture and Interfaces**

#### **Task 2.1: Design Indicator Interface System**
**Estimated Time:** 3 hours
**Assignee:** Lead Developer

**Interface Design:**
```csharp
public interface IIndicator
{
    string Name { get; }
    string Category { get; }
    decimal Weight { get; set; }
    bool IsEnabled { get; set; }
    bool IsReady { get; }
    TimeSpan LastCalculationTime { get; }

    IndicatorSignal Calculate(IndicatorCandle candle, MarketData marketData);
    void Reset();
    IndicatorStatus GetStatus();
}

public class IndicatorStatus
{
    public bool IsOperational { get; set; }
    public string StatusMessage { get; set; }
    public int DataPointsRequired { get; set; }
    public int DataPointsAvailable { get; set; }
    public DateTime LastUpdate { get; set; }
}

public class IndicatorSignal
{
    public string IndicatorName { get; set; }
    public SignalDirection Direction { get; set; }
    public decimal Confidence { get; set; }
    public decimal RawValue { get; set; }
    public DateTime Timestamp { get; set; }
    public string Reason { get; set; }
    public Dictionary<string, object> Metadata { get; set; }

    public static IndicatorSignal Neutral(string indicatorName, string reason) =>
        new IndicatorSignal
        {
            IndicatorName = indicatorName,
            Direction = SignalDirection.Neutral,
            Confidence = 0,
            Reason = reason,
            Timestamp = DateTime.UtcNow,
            Metadata = new Dictionary<string, object>()
        };
}
```

**Acceptance Criteria:**
- Interface supports all required indicator operations
- Signal structure includes metadata for debugging
- Status reporting enables health monitoring
- Performance tracking built into interface

#### **Task 2.2: Implement IndicatorManager**
**Estimated Time:** 4 hours
**Assignee:** Architecture Developer

**IndicatorManager Implementation:**
```csharp
public class IndicatorManager
{
    private readonly List<IIndicator> _indicators;
    private readonly SimpleLogger _logger;
    private readonly PerformanceMonitor _performance;
    private readonly Dictionary<string, decimal> _indicatorWeights;

    public IndicatorManager(SimpleLogger logger, PerformanceMonitor performance)
    {
        _logger = logger;
        _performance = performance;
        _indicators = new List<IIndicator>();
        _indicatorWeights = new Dictionary<string, decimal>();
    }

    public void AddIndicator(IIndicator indicator, decimal weight)
    {
        _indicators.Add(indicator);
        _indicatorWeights[indicator.Name] = weight;
        indicator.Weight = weight;

        _logger.LogInfo($"✅ Added indicator: {indicator.Name} (Weight: {weight:P1})");
    }

    public IndicatorManagerResult ProcessBar(IndicatorCandle candle, MarketData marketData)
    {
        var signals = new List<IndicatorSignal>();
        var totalTime = TimeSpan.Zero;

        using (_performance.StartTiming("IndicatorManager_ProcessBar"))
        {
            foreach (var indicator in _indicators.Where(i => i.IsEnabled))
            {
                try
                {
                    using (_performance.StartTiming($"{indicator.Name}_Calculation"))
                    {
                        var signal = indicator.Calculate(candle, marketData);
                        signal.IndicatorName = indicator.Name;
                        signals.Add(signal);

                        totalTime = totalTime.Add(indicator.LastCalculationTime);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError($"❌ Error in {indicator.Name}: {ex.Message}");
                    signals.Add(IndicatorSignal.Neutral(indicator.Name, $"Error: {ex.Message}"));
                }
            }
        }

        return new IndicatorManagerResult
        {
            Signals = signals,
            TotalProcessingTime = totalTime,
            ActiveIndicatorCount = signals.Count(s => s.Direction != SignalDirection.Neutral),
            AggregatedSignal = AggregateSignals(signals)
        };
    }

    private AggregatedSignal AggregateSignals(List<IndicatorSignal> signals)
    {
        var buySignals = signals.Where(s => s.Direction == SignalDirection.Buy).ToList();
        var sellSignals = signals.Where(s => s.Direction == SignalDirection.Sell).ToList();

        var buyWeight = buySignals.Sum(s => _indicatorWeights[s.IndicatorName] * s.Confidence);
        var sellWeight = sellSignals.Sum(s => _indicatorWeights[s.IndicatorName] * s.Confidence);

        var direction = buyWeight > sellWeight ? SignalDirection.Buy :
                       sellWeight > buyWeight ? SignalDirection.Sell : SignalDirection.Neutral;

        var confidence = Math.Max(buyWeight, sellWeight);
        var alignedCount = direction == SignalDirection.Buy ? buySignals.Count :
                          direction == SignalDirection.Sell ? sellSignals.Count : 0;

        return new AggregatedSignal
        {
            Direction = direction,
            Confidence = Math.Min(confidence, 0.95m),
            AlignedIndicatorCount = alignedCount,
            TotalIndicatorCount = signals.Count,
            BuyWeight = buyWeight,
            SellWeight = sellWeight,
            Timestamp = DateTime.UtcNow
        };
    }
}
```

### **Day 2: Ultra-Fast RSI Implementation**

#### **Task 2.3: Implement Ultra-Fast RSI Indicator**
**Estimated Time:** 4 hours
**Assignee:** Indicator Developer

**Ultra-Fast RSI Implementation:**
```csharp
public class UltraFastRSI : IIndicator
{
    private readonly CircularBuffer<decimal> _prices;
    private readonly CircularBuffer<decimal> _gains;
    private readonly CircularBuffer<decimal> _losses;
    private readonly int _period;
    private readonly decimal _overboughtLevel;
    private readonly decimal _oversoldLevel;

    public string Name => "UltraFastRSI";
    public string Category => "Momentum";
    public decimal Weight { get; set; }
    public bool IsEnabled { get; set; } = true;
    public bool IsReady => _prices.Count >= _period;
    public TimeSpan LastCalculationTime { get; private set; }

    public UltraFastRSI(int period = 3, decimal overboughtLevel = 80, decimal oversoldLevel = 20)
    {
        _period = period;
        _overboughtLevel = overboughtLevel;
        _oversoldLevel = oversoldLevel;
        _prices = new CircularBuffer<decimal>(period + 1);
        _gains = new CircularBuffer<decimal>(period);
        _losses = new CircularBuffer<decimal>(period);
    }

    public IndicatorSignal Calculate(IndicatorCandle candle, MarketData marketData)
    {
        var startTime = DateTime.UtcNow;

        _prices.Add(candle.Close);

        if (_prices.Count < 2)
            return IndicatorSignal.Neutral(Name, "Insufficient price data");

        // Calculate price change
        var priceChange = _prices[_prices.Count - 1] - _prices[_prices.Count - 2];

        _gains.Add(priceChange > 0 ? priceChange : 0);
        _losses.Add(priceChange < 0 ? Math.Abs(priceChange) : 0);

        if (!IsReady)
            return IndicatorSignal.Neutral(Name, "Warming up");

        var rsi = CalculateRSI();
        var signal = GenerateSignal(rsi);

        LastCalculationTime = DateTime.UtcNow - startTime;

        return signal;
    }

    private decimal CalculateRSI()
    {
        var avgGain = _gains.Sum() / _period;
        var avgLoss = _losses.Sum() / _period;

        if (avgLoss == 0) return 100;
        if (avgGain == 0) return 0;

        var rs = avgGain / avgLoss;
        return 100 - (100 / (1 + rs));
    }

    private IndicatorSignal GenerateSignal(decimal rsi)
    {
        var signal = new IndicatorSignal
        {
            IndicatorName = Name,
            RawValue = rsi,
            Timestamp = DateTime.UtcNow,
            Metadata = new Dictionary<string, object>
            {
                ["RSI"] = rsi,
                ["OverboughtLevel"] = _overboughtLevel,
                ["OversoldLevel"] = _oversoldLevel
            }
        };

        if (rsi > _overboughtLevel)
        {
            signal.Direction = SignalDirection.Sell;
            signal.Confidence = Math.Min(0.9m, 0.5m + (rsi - _overboughtLevel) / 20m * 0.4m);
            signal.Reason = $"RSI Overbought: {rsi:F1} > {_overboughtLevel}";
        }
        else if (rsi < _oversoldLevel)
        {
            signal.Direction = SignalDirection.Buy;
            signal.Confidence = Math.Min(0.9m, 0.5m + (_oversoldLevel - rsi) / 20m * 0.4m);
            signal.Reason = $"RSI Oversold: {rsi:F1} < {_oversoldLevel}";
        }
        else
        {
            signal.Direction = SignalDirection.Neutral;
            signal.Confidence = 0;
            signal.Reason = $"RSI Neutral: {rsi:F1}";
        }

        return signal;
    }

    public void Reset()
    {
        _prices.Clear();
        _gains.Clear();
        _losses.Clear();
    }

    public IndicatorStatus GetStatus()
    {
        return new IndicatorStatus
        {
            IsOperational = IsEnabled && IsReady,
            StatusMessage = IsReady ? "Operational" : $"Warming up ({_prices.Count}/{_period})",
            DataPointsRequired = _period,
            DataPointsAvailable = _prices.Count,
            LastUpdate = DateTime.UtcNow
        };
    }
}
```

### **Day 3: Bollinger Bands Implementation**

#### **Task 2.4: Implement Bollinger Bands Indicator**
**Estimated Time:** 4 hours
**Assignee:** Indicator Developer

**Bollinger Bands Implementation:**
```csharp
public class BollingerBands : IIndicator
{
    private readonly CircularBuffer<decimal> _prices;
    private readonly int _period;
    private readonly decimal _standardDeviations;
    private readonly PriceSource _priceSource;

    public string Name => "BollingerBands";
    public string Category => "Volatility";
    public decimal Weight { get; set; }
    public bool IsEnabled { get; set; } = true;
    public bool IsReady => _prices.Count >= _period;
    public TimeSpan LastCalculationTime { get; private set; }

    public BollingerBands(int period = 20, decimal standardDeviations = 2.0m, PriceSource priceSource = PriceSource.Close)
    {
        _period = period;
        _standardDeviations = standardDeviations;
        _priceSource = priceSource;
        _prices = new CircularBuffer<decimal>(period);
    }

    public IndicatorSignal Calculate(IndicatorCandle candle, MarketData marketData)
    {
        var startTime = DateTime.UtcNow;

        var price = GetPrice(candle, _priceSource);
        _prices.Add(price);

        if (!IsReady)
            return IndicatorSignal.Neutral(Name, "Insufficient data");

        var (upperBand, middleBand, lowerBand) = CalculateBands();
        var signal = GenerateSignal(price, upperBand, middleBand, lowerBand);

        LastCalculationTime = DateTime.UtcNow - startTime;

        return signal;
    }

    private (decimal upper, decimal middle, decimal lower) CalculateBands()
    {
        var sma = _prices.Sum() / _period;

        var variance = _prices.ToArray().Sum(price => (price - sma) * (price - sma)) / _period;
        var stdDev = (decimal)Math.Sqrt((double)variance);

        var upperBand = sma + (_standardDeviations * stdDev);
        var lowerBand = sma - (_standardDeviations * stdDev);

        return (upperBand, sma, lowerBand);
    }

    private IndicatorSignal GenerateSignal(decimal price, decimal upperBand, decimal middleBand, decimal lowerBand)
    {
        var signal = new IndicatorSignal
        {
            IndicatorName = Name,
            RawValue = price,
            Timestamp = DateTime.UtcNow,
            Metadata = new Dictionary<string, object>
            {
                ["Price"] = price,
                ["UpperBand"] = upperBand,
                ["MiddleBand"] = middleBand,
                ["LowerBand"] = lowerBand,
                ["BandWidth"] = upperBand - lowerBand
            }
        };

        var upperDistance = (upperBand - price) / (upperBand - middleBand);
        var lowerDistance = (price - lowerBand) / (middleBand - lowerBand);

        if (price >= upperBand)
        {
            signal.Direction = SignalDirection.Sell;
            signal.Confidence = Math.Min(0.85m, 0.6m + Math.Min(0.25m, (price - upperBand) / upperBand * 2));
            signal.Reason = $"Price above upper band: {price:F4} >= {upperBand:F4}";
        }
        else if (price <= lowerBand)
        {
            signal.Direction = SignalDirection.Buy;
            signal.Confidence = Math.Min(0.85m, 0.6m + Math.Min(0.25m, (lowerBand - price) / lowerBand * 2));
            signal.Reason = $"Price below lower band: {price:F4} <= {lowerBand:F4}";
        }
        else if (price > middleBand && upperDistance < 0.3m)
        {
            signal.Direction = SignalDirection.Sell;
            signal.Confidence = 0.4m + (1 - upperDistance) * 0.3m;
            signal.Reason = $"Price near upper band: {upperDistance:P1} from upper";
        }
        else if (price < middleBand && lowerDistance < 0.3m)
        {
            signal.Direction = SignalDirection.Buy;
            signal.Confidence = 0.4m + (1 - lowerDistance) * 0.3m;
            signal.Reason = $"Price near lower band: {lowerDistance:P1} from lower";
        }
        else
        {
            signal.Direction = SignalDirection.Neutral;
            signal.Confidence = 0;
            signal.Reason = $"Price in middle range: {price:F4}";
        }

        return signal;
    }

    private decimal GetPrice(IndicatorCandle candle, PriceSource source)
    {
        return source switch
        {
            PriceSource.Open => candle.Open,
            PriceSource.High => candle.High,
            PriceSource.Low => candle.Low,
            PriceSource.Close => candle.Close,
            PriceSource.Typical => (candle.High + candle.Low + candle.Close) / 3,
            _ => candle.Close
        };
    }

    public void Reset()
    {
        _prices.Clear();
    }

    public IndicatorStatus GetStatus()
    {
        return new IndicatorStatus
        {
            IsOperational = IsEnabled && IsReady,
            StatusMessage = IsReady ? "Operational" : $"Warming up ({_prices.Count}/{_period})",
            DataPointsRequired = _period,
            DataPointsAvailable = _prices.Count,
            LastUpdate = DateTime.UtcNow
        };
    }
}

public enum PriceSource
{
    Open,
    High,
    Low,
    Close,
    Typical
}
```

### **Day 4: Stochastic Oscillator and MACD Implementation**

#### **Task 2.5: Implement Stochastic Oscillator**
**Estimated Time:** 4 hours
**Assignee:** Indicator Developer

**Stochastic Oscillator Implementation:**
```csharp
public class StochasticOscillator : IIndicator
{
    private readonly CircularBuffer<decimal> _highs;
    private readonly CircularBuffer<decimal> _lows;
    private readonly CircularBuffer<decimal> _closes;
    private readonly CircularBuffer<decimal> _kValues;
    private readonly int _kPeriod;
    private readonly int _dPeriod;
    private readonly decimal _overboughtLevel;
    private readonly decimal _oversoldLevel;

    public string Name => "StochasticOscillator";
    public string Category => "Momentum";
    public decimal Weight { get; set; }
    public bool IsEnabled { get; set; } = true;
    public bool IsReady => _highs.Count >= _kPeriod && _kValues.Count >= _dPeriod;
    public TimeSpan LastCalculationTime { get; private set; }

    public StochasticOscillator(int kPeriod = 14, int dPeriod = 3, decimal overboughtLevel = 80, decimal oversoldLevel = 20)
    {
        _kPeriod = kPeriod;
        _dPeriod = dPeriod;
        _overboughtLevel = overboughtLevel;
        _oversoldLevel = oversoldLevel;

        _highs = new CircularBuffer<decimal>(kPeriod);
        _lows = new CircularBuffer<decimal>(kPeriod);
        _closes = new CircularBuffer<decimal>(kPeriod);
        _kValues = new CircularBuffer<decimal>(dPeriod);
    }

    public IndicatorSignal Calculate(IndicatorCandle candle, MarketData marketData)
    {
        var startTime = DateTime.UtcNow;

        _highs.Add(candle.High);
        _lows.Add(candle.Low);
        _closes.Add(candle.Close);

        if (_highs.Count < _kPeriod)
            return IndicatorSignal.Neutral(Name, "Insufficient data for %K");

        var kValue = CalculateK();
        _kValues.Add(kValue);

        if (!IsReady)
            return IndicatorSignal.Neutral(Name, "Insufficient data for %D");

        var dValue = _kValues.Sum() / _dPeriod;
        var signal = GenerateSignal(kValue, dValue);

        LastCalculationTime = DateTime.UtcNow - startTime;

        return signal;
    }

    private decimal CalculateK()
    {
        var highestHigh = _highs.ToArray().Max();
        var lowestLow = _lows.ToArray().Min();
        var currentClose = _closes[_closes.Count - 1];

        if (highestHigh == lowestLow) return 50; // Avoid division by zero

        return ((currentClose - lowestLow) / (highestHigh - lowestLow)) * 100;
    }

    private IndicatorSignal GenerateSignal(decimal kValue, decimal dValue)
    {
        var signal = new IndicatorSignal
        {
            IndicatorName = Name,
            RawValue = kValue,
            Timestamp = DateTime.UtcNow,
            Metadata = new Dictionary<string, object>
            {
                ["K"] = kValue,
                ["D"] = dValue,
                ["OverboughtLevel"] = _overboughtLevel,
                ["OversoldLevel"] = _oversoldLevel
            }
        };

        // Check for crossover signals
        var previousK = _kValues.Count > 1 ? _kValues[_kValues.Count - 2] : kValue;
        var kCrossedAboveD = previousK <= dValue && kValue > dValue;
        var kCrossedBelowD = previousK >= dValue && kValue < dValue;

        if (kValue > _overboughtLevel && kCrossedBelowD)
        {
            signal.Direction = SignalDirection.Sell;
            signal.Confidence = Math.Min(0.85m, 0.6m + (kValue - _overboughtLevel) / 20m * 0.25m);
            signal.Reason = $"Stoch Overbought Crossover: %K({kValue:F1}) crossed below %D({dValue:F1})";
        }
        else if (kValue < _oversoldLevel && kCrossedAboveD)
        {
            signal.Direction = SignalDirection.Buy;
            signal.Confidence = Math.Min(0.85m, 0.6m + (_oversoldLevel - kValue) / 20m * 0.25m);
            signal.Reason = $"Stoch Oversold Crossover: %K({kValue:F1}) crossed above %D({dValue:F1})";
        }
        else if (kValue > _overboughtLevel)
        {
            signal.Direction = SignalDirection.Sell;
            signal.Confidence = 0.4m + (kValue - _overboughtLevel) / 20m * 0.2m;
            signal.Reason = $"Stoch Overbought: %K({kValue:F1}) > {_overboughtLevel}";
        }
        else if (kValue < _oversoldLevel)
        {
            signal.Direction = SignalDirection.Buy;
            signal.Confidence = 0.4m + (_oversoldLevel - kValue) / 20m * 0.2m;
            signal.Reason = $"Stoch Oversold: %K({kValue:F1}) < {_oversoldLevel}";
        }
        else
        {
            signal.Direction = SignalDirection.Neutral;
            signal.Confidence = 0;
            signal.Reason = $"Stoch Neutral: %K({kValue:F1}), %D({dValue:F1})";
        }

        return signal;
    }

    public void Reset()
    {
        _highs.Clear();
        _lows.Clear();
        _closes.Clear();
        _kValues.Clear();
    }

    public IndicatorStatus GetStatus()
    {
        return new IndicatorStatus
        {
            IsOperational = IsEnabled && IsReady,
            StatusMessage = IsReady ? "Operational" : $"Warming up (K:{_highs.Count}/{_kPeriod}, D:{_kValues.Count}/{_dPeriod})",
            DataPointsRequired = _kPeriod + _dPeriod,
            DataPointsAvailable = _highs.Count + _kValues.Count,
            LastUpdate = DateTime.UtcNow
        };
    }
}
```

#### **Task 2.6: Implement MACD Indicator**
**Estimated Time:** 4 hours
**Assignee:** Indicator Developer

**MACD Implementation:**
```csharp
public class MACD : IIndicator
{
    private readonly CircularBuffer<decimal> _prices;
    private readonly int _fastPeriod;
    private readonly int _slowPeriod;
    private readonly int _signalPeriod;
    private readonly CircularBuffer<decimal> _macdValues;

    public string Name => "MACD";
    public string Category => "Trend";
    public decimal Weight { get; set; }
    public bool IsEnabled { get; set; } = true;
    public bool IsReady => _prices.Count >= _slowPeriod && _macdValues.Count >= _signalPeriod;
    public TimeSpan LastCalculationTime { get; private set; }

    public MACD(int fastPeriod = 12, int slowPeriod = 26, int signalPeriod = 9)
    {
        _fastPeriod = fastPeriod;
        _slowPeriod = slowPeriod;
        _signalPeriod = signalPeriod;

        _prices = new CircularBuffer<decimal>(slowPeriod);
        _macdValues = new CircularBuffer<decimal>(signalPeriod);
    }

    public IndicatorSignal Calculate(IndicatorCandle candle, MarketData marketData)
    {
        var startTime = DateTime.UtcNow;

        _prices.Add(candle.Close);

        if (_prices.Count < _slowPeriod)
            return IndicatorSignal.Neutral(Name, "Insufficient data for MACD");

        var macdValue = CalculateMACD();
        _macdValues.Add(macdValue);

        if (!IsReady)
            return IndicatorSignal.Neutral(Name, "Insufficient data for Signal line");

        var signalValue = _macdValues.Sum() / _signalPeriod;
        var histogram = macdValue - signalValue;
        var signal = GenerateSignal(macdValue, signalValue, histogram);

        LastCalculationTime = DateTime.UtcNow - startTime;

        return signal;
    }

    private decimal CalculateMACD()
    {
        var fastEMA = CalculateEMA(_fastPeriod);
        var slowEMA = CalculateEMA(_slowPeriod);
        return fastEMA - slowEMA;
    }

    private decimal CalculateEMA(int period)
    {
        var multiplier = 2m / (period + 1);
        var prices = _prices.ToArray().TakeLast(period).ToArray();

        var ema = prices[0]; // Start with first price

        for (int i = 1; i < prices.Length; i++)
        {
            ema = (prices[i] * multiplier) + (ema * (1 - multiplier));
        }

        return ema;
    }

    private IndicatorSignal GenerateSignal(decimal macdValue, decimal signalValue, decimal histogram)
    {
        var signal = new IndicatorSignal
        {
            IndicatorName = Name,
            RawValue = macdValue,
            Timestamp = DateTime.UtcNow,
            Metadata = new Dictionary<string, object>
            {
                ["MACD"] = macdValue,
                ["Signal"] = signalValue,
                ["Histogram"] = histogram
            }
        };

        // Check for crossover signals
        var previousMACD = _macdValues.Count > 1 ? _macdValues[_macdValues.Count - 2] : macdValue;
        var previousSignal = _macdValues.Count >= _signalPeriod ?
            _macdValues.ToArray().TakeLast(_signalPeriod).SkipLast(1).Sum() / (_signalPeriod - 1) : signalValue;

        var macdCrossedAbove = previousMACD <= previousSignal && macdValue > signalValue;
        var macdCrossedBelow = previousMACD >= previousSignal && macdValue < signalValue;

        if (macdCrossedAbove && macdValue > 0)
        {
            signal.Direction = SignalDirection.Buy;
            signal.Confidence = Math.Min(0.8m, 0.6m + Math.Abs(histogram) * 0.2m);
            signal.Reason = $"MACD Bullish Crossover: MACD({macdValue:F4}) > Signal({signalValue:F4})";
        }
        else if (macdCrossedBelow && macdValue < 0)
        {
            signal.Direction = SignalDirection.Sell;
            signal.Confidence = Math.Min(0.8m, 0.6m + Math.Abs(histogram) * 0.2m);
            signal.Reason = $"MACD Bearish Crossover: MACD({macdValue:F4}) < Signal({signalValue:F4})";
        }
        else if (histogram > 0 && macdValue > signalValue)
        {
            signal.Direction = SignalDirection.Buy;
            signal.Confidence = 0.3m + Math.Min(0.3m, Math.Abs(histogram) * 0.1m);
            signal.Reason = $"MACD Above Signal: Histogram({histogram:F4}) > 0";
        }
        else if (histogram < 0 && macdValue < signalValue)
        {
            signal.Direction = SignalDirection.Sell;
            signal.Confidence = 0.3m + Math.Min(0.3m, Math.Abs(histogram) * 0.1m);
            signal.Reason = $"MACD Below Signal: Histogram({histogram:F4}) < 0";
        }
        else
        {
            signal.Direction = SignalDirection.Neutral;
            signal.Confidence = 0;
            signal.Reason = $"MACD Neutral: MACD({macdValue:F4}), Signal({signalValue:F4})";
        }

        return signal;
    }

    public void Reset()
    {
        _prices.Clear();
        _macdValues.Clear();
    }

    public IndicatorStatus GetStatus()
    {
        return new IndicatorStatus
        {
            IsOperational = IsEnabled && IsReady,
            StatusMessage = IsReady ? "Operational" : $"Warming up (Prices:{_prices.Count}/{_slowPeriod}, Signal:{_macdValues.Count}/{_signalPeriod})",
            DataPointsRequired = _slowPeriod + _signalPeriod,
            DataPointsAvailable = _prices.Count + _macdValues.Count,
            LastUpdate = DateTime.UtcNow
        };
    }
}
```

### **Day 5: Strategy Integration and UI Enhancement**

#### **Task 2.7: Update Main Strategy Class**
**Estimated Time:** 4 hours
**Assignee:** Lead Developer

**Enhanced Strategy Implementation:**
```csharp
[Display(Name = "High-Probability Scalping v2.0 - Phase 2")]
public class HighProbabilityScalpingV2Strategy : ChartStrategy
{
    #region User Settings - Phase 1 Indicators

    [Display(Name = "VFI Period", GroupName = "VFI Settings", Order = 1)]
    [Range(5, 50)]
    public int VFIPeriod { get; set; } = 14;

    [Display(Name = "VFI Buy Threshold", GroupName = "VFI Settings", Order = 2)]
    [Range(0.5, 3.0)]
    public decimal VFIBuyThreshold { get; set; } = 1.3m;

    [Display(Name = "VFI Sell Threshold", GroupName = "VFI Settings", Order = 3)]
    [Range(0.1, 1.5)]
    public decimal VFISellThreshold { get; set; } = 0.7m;

    [Display(Name = "VFI Weight %", GroupName = "VFI Settings", Order = 4)]
    [Range(10, 50)]
    public int VFIWeight { get; set; } = 30;

    [Display(Name = "VFI Enabled", GroupName = "VFI Settings", Order = 5)]
    public bool VFIEnabled { get; set; } = true;

    [Display(Name = "RSI Period", GroupName = "RSI Settings", Order = 10)]
    [Range(2, 14)]
    public int RSIPeriod { get; set; } = 3;

    [Display(Name = "RSI Overbought", GroupName = "RSI Settings", Order = 11)]
    [Range(70, 90)]
    public int RSIOverbought { get; set; } = 80;

    [Display(Name = "RSI Oversold", GroupName = "RSI Settings", Order = 12)]
    [Range(10, 30)]
    public int RSIOversold { get; set; } = 20;

    [Display(Name = "RSI Weight %", GroupName = "RSI Settings", Order = 13)]
    [Range(10, 40)]
    public int RSIWeight { get; set; } = 20;

    [Display(Name = "RSI Enabled", GroupName = "RSI Settings", Order = 14)]
    public bool RSIEnabled { get; set; } = true;

    [Display(Name = "BB Period", GroupName = "Bollinger Bands", Order = 20)]
    [Range(10, 50)]
    public int BBPeriod { get; set; } = 20;

    [Display(Name = "BB Std Deviations", GroupName = "Bollinger Bands", Order = 21)]
    [Range(1.0, 3.0)]
    public decimal BBStdDev { get; set; } = 2.0m;

    [Display(Name = "BB Weight %", GroupName = "Bollinger Bands", Order = 22)]
    [Range(10, 40)]
    public int BBWeight { get; set; } = 25;

    [Display(Name = "BB Enabled", GroupName = "Bollinger Bands", Order = 23)]
    public bool BBEnabled { get; set; } = true;

    [Display(Name = "Stoch %K Period", GroupName = "Stochastic", Order = 30)]
    [Range(5, 30)]
    public int StochKPeriod { get; set; } = 14;

    [Display(Name = "Stoch %D Period", GroupName = "Stochastic", Order = 31)]
    [Range(1, 10)]
    public int StochDPeriod { get; set; } = 3;

    [Display(Name = "Stoch Overbought", GroupName = "Stochastic", Order = 32)]
    [Range(70, 90)]
    public int StochOverbought { get; set; } = 80;

    [Display(Name = "Stoch Oversold", GroupName = "Stochastic", Order = 33)]
    [Range(10, 30)]
    public int StochOversold { get; set; } = 20;

    [Display(Name = "Stoch Weight %", GroupName = "Stochastic", Order = 34)]
    [Range(10, 40)]
    public int StochWeight { get; set; } = 25;

    [Display(Name = "Stoch Enabled", GroupName = "Stochastic", Order = 35)]
    public bool StochEnabled { get; set; } = true;

    [Display(Name = "MACD Fast Period", GroupName = "MACD Settings", Order = 40)]
    [Range(5, 20)]
    public int MACDFast { get; set; } = 12;

    [Display(Name = "MACD Slow Period", GroupName = "MACD Settings", Order = 41)]
    [Range(15, 50)]
    public int MACDSlow { get; set; } = 26;

    [Display(Name = "MACD Signal Period", GroupName = "MACD Settings", Order = 42)]
    [Range(3, 15)]
    public int MACDSignal { get; set; } = 9;

    [Display(Name = "MACD Weight %", GroupName = "MACD Settings", Order = 43)]
    [Range(10, 40)]
    public int MACDWeight { get; set; } = 20;

    [Display(Name = "MACD Enabled", GroupName = "MACD Settings", Order = 44)]
    public bool MACDEnabled { get; set; } = true;

    #endregion

    #region Signal Coordination Settings

    [Display(Name = "Min Confidence %", GroupName = "Signal Settings", Order = 50)]
    [Range(60, 95)]
    public int MinConfidence { get; set; } = 80;

    [Display(Name = "Min Indicator Alignment", GroupName = "Signal Settings", Order = 51)]
    [Range(2, 5)]
    public int MinIndicatorAlignment { get; set; } = 3;

    [Display(Name = "Enable Debug Logging", GroupName = "Debug", Order = 60)]
    public bool EnableDebugLogging { get; set; } = true;

    #endregion

    private IndicatorManager _indicatorManager;
    private SimpleLogger _logger;
    private PerformanceMonitor _performance;

    protected override void OnStarted()
    {
        InitializeComponents();
        ValidateConfiguration();
        LogStartupInfo();
    }

    protected override void OnCalculate(int bar, decimal value)
    {
        var maxPeriod = Math.Max(Math.Max(VFIPeriod, RSIPeriod), Math.Max(BBPeriod, Math.Max(StochKPeriod, MACDSlow)));
        if (bar < maxPeriod) return;

        ProcessIndicatorSignals(bar);
    }

    private void InitializeComponents()
    {
        _logger = new SimpleLogger("HighProbabilityScalpingV2_Phase2", EnableDebugLogging);
        _performance = new PerformanceMonitor(_logger);
        _indicatorManager = new IndicatorManager(_logger, _performance);

        // Add all indicators with their weights
        var vfi = new VolumeFlowIndex(VFIPeriod, VFIBuyThreshold, VFISellThreshold) { IsEnabled = VFIEnabled };
        _indicatorManager.AddIndicator(vfi, VFIWeight / 100m);

        var rsi = new UltraFastRSI(RSIPeriod, RSIOverbought, RSIOversold) { IsEnabled = RSIEnabled };
        _indicatorManager.AddIndicator(rsi, RSIWeight / 100m);

        var bb = new BollingerBands(BBPeriod, BBStdDev) { IsEnabled = BBEnabled };
        _indicatorManager.AddIndicator(bb, BBWeight / 100m);

        var stoch = new StochasticOscillator(StochKPeriod, StochDPeriod, StochOverbought, StochOversold) { IsEnabled = StochEnabled };
        _indicatorManager.AddIndicator(stoch, StochWeight / 100m);

        var macd = new MACD(MACDFast, MACDSlow, MACDSignal) { IsEnabled = MACDEnabled };
        _indicatorManager.AddIndicator(macd, MACDWeight / 100m);

        _logger.LogInfo("✅ All Phase 1 indicators initialized successfully");
    }

    private void ProcessIndicatorSignals(int bar)
    {
        try
        {
            IndicatorManagerResult result;

            using (_performance.StartTiming("TotalIndicatorProcessing"))
            {
                var candle = GetCandle(bar);
                var marketData = new MarketData(); // Placeholder for future market data
                result = _indicatorManager.ProcessBar(candle, marketData);
            }

            LogSignalResults(result);

            // Check if signal meets trading criteria
            if (result.AggregatedSignal.Confidence >= MinConfidence / 100m &&
                result.AggregatedSignal.AlignedIndicatorCount >= MinIndicatorAlignment)
            {
                _logger.LogInfo($"🎯 TRADING SIGNAL: {result.AggregatedSignal.Direction} @ {result.AggregatedSignal.Confidence:P1} " +
                              $"(Aligned: {result.AggregatedSignal.AlignedIndicatorCount}/{result.AggregatedSignal.TotalIndicatorCount})");

                // Future: Add trade execution logic here
            }

            // Performance reporting every 100 bars
            if (bar % 100 == 0)
            {
                _performance.LogPerformanceReport();
                LogIndicatorStatus();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"Error in ProcessIndicatorSignals: {ex.Message}");
        }
    }

    private void LogSignalResults(IndicatorManagerResult result)
    {
        var activeSignals = result.Signals.Where(s => s.Direction != SignalDirection.Neutral).ToList();

        _logger.LogInfo($"📊 Phase1 Signals: {activeSignals.Count}/{result.Signals.Count} active " +
                       $"(Confidence: {result.AggregatedSignal.Confidence:P1}, " +
                       $"Aligned: {result.AggregatedSignal.AlignedIndicatorCount}, " +
                       $"Processing: {result.TotalProcessingTime.TotalMilliseconds:F1}ms)");

        foreach (var signal in activeSignals)
        {
            _logger.LogDebug($"  └─ {signal.IndicatorName}: {signal.Direction} @ {signal.Confidence:P1} - {signal.Reason}");
        }
    }

    private void LogIndicatorStatus()
    {
        _logger.LogInfo("=== INDICATOR STATUS REPORT ===");
        foreach (var indicator in _indicatorManager.GetIndicators())
        {
            var status = indicator.GetStatus();
            _logger.LogInfo($"{indicator.Name}: {status.StatusMessage} (Weight: {indicator.Weight:P1})");
        }
    }
}
```
```
```