using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using HighProbabilityScalpingV2.ATAS.Components.Indicators;
using HighProbabilityScalpingV2.ATAS.Components.Models;

namespace HighProbabilityScalpingV2.Tests.IndicatorTests
{
    /// <summary>
    /// Unit tests for VolumeFlowIndex indicator
    /// Validates Phase 1 success criteria:
    /// - VFI generates signals >70% confidence
    /// - Performance <5ms per calculation
    /// - Proper signal generation logic
    /// </summary>
    [TestClass]
    public class VolumeFlowIndexTests
    {
        private VolumeFlowIndex _vfi;

        [TestInitialize]
        public void Setup()
        {
            _vfi = new VolumeFlowIndex(period: 3, buyThreshold: 1.0m, sellThreshold: 0.5m);
        }

        [TestMethod]
        public void VFI_WithInsufficientData_ReturnsNeutral()
        {
            // Arrange
            var candle = CreateTestCandle(100, 1000);

            // Act
            var signal = _vfi.Calculate(candle);

            // Assert
            Assert.AreEqual(SignalDirection.Neutral, signal.Direction);
            Assert.AreEqual(0, signal.Confidence);
            Assert.IsTrue(signal.Reason.Contains("Insufficient data"));
            Assert.AreEqual("VFI", signal.IndicatorName);
        }

        [TestMethod]
        public void VFI_WithSufficientData_GeneratesSignals()
        {
            // Arrange - Add enough data to make indicator ready
            for (int i = 0; i < 5; i++)
            {
                var candle = CreateTestCandle(100 + i * 2, 1000); // Rising prices
                var signal = _vfi.Calculate(candle);

                if (i >= 2) // After sufficient data
                {
                    // Assert
                    Assert.IsTrue(_vfi.IsReady, "VFI should be ready after sufficient data");
                    Assert.AreNotEqual(SignalDirection.Neutral, signal.Direction, "Should generate non-neutral signals");
                    Assert.IsTrue(signal.Confidence >= 0, "Confidence should be non-negative");
                    Assert.IsTrue(signal.Confidence <= 1, "Confidence should not exceed 1.0");
                }
            }
        }

        [TestMethod]
        public void VFI_WithRisingPrices_GeneratesBuySignals()
        {
            // Arrange - Create strong uptrend
            var prices = new decimal[] { 100, 102, 105, 108, 112 };
            IndicatorSignal? lastSignal = null;

            // Act
            foreach (var price in prices)
            {
                var candle = CreateTestCandle(price, 1000);
                lastSignal = _vfi.Calculate(candle);
            }

            // Assert
            Assert.IsNotNull(lastSignal);
            Assert.IsTrue(_vfi.IsReady);
            
            // Should generate buy signal for strong uptrend
            if (lastSignal.Direction == SignalDirection.Buy)
            {
                Assert.IsTrue(lastSignal.Confidence >= 0.6m, "Buy signal should have at least 60% confidence");
                Assert.IsTrue(lastSignal.RawValue > 0, "VFI value should be positive for buy signals");
            }
        }

        [TestMethod]
        public void VFI_WithFallingPrices_GeneratesSellSignals()
        {
            // Arrange - Create strong downtrend
            var prices = new decimal[] { 112, 108, 105, 102, 100 };
            IndicatorSignal? lastSignal = null;

            // Act
            foreach (var price in prices)
            {
                var candle = CreateTestCandle(price, 1000);
                lastSignal = _vfi.Calculate(candle);
            }

            // Assert
            Assert.IsNotNull(lastSignal);
            Assert.IsTrue(_vfi.IsReady);
            
            // Should generate sell signal for strong downtrend
            if (lastSignal.Direction == SignalDirection.Sell)
            {
                Assert.IsTrue(lastSignal.Confidence >= 0.6m, "Sell signal should have at least 60% confidence");
                Assert.IsTrue(lastSignal.RawValue < 0, "VFI value should be negative for sell signals");
            }
        }

        [TestMethod]
        public void VFI_PerformanceTest_MeetsTargetTime()
        {
            // Arrange - Initialize with data
            for (int i = 0; i < 10; i++)
            {
                _vfi.Calculate(CreateTestCandle(100 + i, 1000));
            }

            // Act - Measure performance of single calculation
            var startTime = DateTime.UtcNow;
            var candle = CreateTestCandle(120, 1000);
            _vfi.Calculate(candle);
            var duration = DateTime.UtcNow - startTime;

            // Assert - Should meet <5ms target
            Assert.IsTrue(duration.TotalMilliseconds < 5, 
                $"VFI calculation took {duration.TotalMilliseconds:F2}ms, target: <5ms");
        }

        [TestMethod]
        public void VFI_MultipleCalculations_MaintainsPerformance()
        {
            // Arrange
            var totalTime = TimeSpan.Zero;
            var calculations = 100;

            // Act - Perform multiple calculations
            for (int i = 0; i < calculations; i++)
            {
                var startTime = DateTime.UtcNow;
                var candle = CreateTestCandle(100 + i % 20, 1000); // Varying prices
                _vfi.Calculate(candle);
                totalTime += DateTime.UtcNow - startTime;
            }

            // Assert
            var averageTime = totalTime.TotalMilliseconds / calculations;
            Assert.IsTrue(averageTime < 5, 
                $"Average VFI calculation time {averageTime:F2}ms exceeds 5ms target");
        }

        [TestMethod]
        public void VFI_HighConfidenceSignals_MeetThreshold()
        {
            // Arrange - Create very strong trend
            var strongUptrend = new decimal[] { 100, 105, 112, 120, 130 };
            
            // Act
            IndicatorSignal? signal = null;
            foreach (var price in strongUptrend)
            {
                var candle = CreateTestCandle(price, 2000); // High volume
                signal = _vfi.Calculate(candle);
            }

            // Assert
            Assert.IsNotNull(signal);
            if (signal.Direction != SignalDirection.Neutral)
            {
                Assert.IsTrue(signal.ConfidencePercent >= 70, 
                    $"Strong trend should generate >70% confidence, got {signal.ConfidencePercent:F1}%");
            }
        }

        [TestMethod]
        public void VFI_Configuration_ValidatesParameters()
        {
            // Test invalid period
            Assert.ThrowsException<ArgumentException>(() => 
                new VolumeFlowIndex(period: 0, buyThreshold: 1.0m, sellThreshold: 0.5m));

            // Test invalid buy threshold
            Assert.ThrowsException<ArgumentException>(() => 
                new VolumeFlowIndex(period: 14, buyThreshold: -1.0m, sellThreshold: 0.5m));

            // Test invalid sell threshold
            Assert.ThrowsException<ArgumentException>(() => 
                new VolumeFlowIndex(period: 14, buyThreshold: 1.0m, sellThreshold: -0.5m));
        }

        [TestMethod]
        public void VFI_Reset_ClearsState()
        {
            // Arrange - Add some data
            for (int i = 0; i < 5; i++)
            {
                _vfi.Calculate(CreateTestCandle(100 + i, 1000));
            }
            
            Assert.IsTrue(_vfi.IsReady);
            Assert.IsTrue(_vfi.CalculationCount > 0);

            // Act
            _vfi.Reset();

            // Assert
            Assert.IsFalse(_vfi.IsReady);
            Assert.AreEqual(0, _vfi.CalculationCount);
            Assert.AreEqual(0, _vfi.CurrentValue);
        }

        [TestMethod]
        public void VFI_Status_ProvidesDebugInfo()
        {
            // Arrange
            _vfi.Calculate(CreateTestCandle(100, 1000));

            // Act
            var status = _vfi.GetStatus();

            // Assert
            Assert.IsNotNull(status);
            Assert.IsTrue(status.Contains("VFI"));
            Assert.IsTrue(status.Contains("Ready"));
            Assert.IsTrue(status.Contains("Value"));
        }

        /// <summary>
        /// Helper method to create test candles
        /// </summary>
        private IndicatorCandle CreateTestCandle(decimal price, decimal volume, DateTime? time = null)
        {
            return new IndicatorCandle
            {
                Open = price - 0.5m,
                High = price + 1,
                Low = price - 1,
                Close = price,
                Volume = volume,
                Time = time ?? DateTime.UtcNow
            };
        }
    }
}
