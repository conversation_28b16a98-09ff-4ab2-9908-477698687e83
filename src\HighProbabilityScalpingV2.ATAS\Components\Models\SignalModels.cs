using System;

namespace HighProbabilityScalpingV2.ATAS.Components.Models
{
    /// <summary>
    /// Represents the direction of a trading signal
    /// </summary>
    public enum SignalDirection
    {
        /// <summary>
        /// No clear direction - neutral signal
        /// </summary>
        Neutral = 0,

        /// <summary>
        /// Bullish signal - suggests buying
        /// </summary>
        Buy = 1,

        /// <summary>
        /// Bearish signal - suggests selling
        /// </summary>
        Sell = -1
    }

    /// <summary>
    /// Represents a trading signal generated by an indicator
    /// Contains direction, confidence level, and reasoning
    /// </summary>
    public class IndicatorSignal
    {
        /// <summary>
        /// Direction of the signal (Buy, Sell, or Neutral)
        /// </summary>
        public SignalDirection Direction { get; set; }

        /// <summary>
        /// Confidence level of the signal (0.0 to 1.0)
        /// </summary>
        public decimal Confidence { get; set; }

        /// <summary>
        /// Timestamp when the signal was generated
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Human-readable reason for the signal
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// Name of the indicator that generated this signal
        /// </summary>
        public string IndicatorName { get; set; }

        /// <summary>
        /// Raw indicator value that generated the signal
        /// </summary>
        public decimal RawValue { get; set; }

        /// <summary>
        /// Creates a new indicator signal
        /// </summary>
        /// <param name="direction">Signal direction</param>
        /// <param name="confidence">Confidence level (0.0 to 1.0)</param>
        /// <param name="reason">Reason for the signal</param>
        /// <param name="indicatorName">Name of the indicator</param>
        /// <param name="rawValue">Raw indicator value</param>
        public IndicatorSignal(SignalDirection direction, decimal confidence, string reason, 
            string indicatorName = "", decimal rawValue = 0)
        {
            Direction = direction;
            Confidence = Math.Max(0, Math.Min(1, confidence)); // Clamp to [0, 1]
            Timestamp = DateTime.UtcNow;
            Reason = reason ?? string.Empty;
            IndicatorName = indicatorName ?? string.Empty;
            RawValue = rawValue;
        }

        /// <summary>
        /// Creates a neutral signal with specified reason
        /// </summary>
        /// <param name="reason">Reason for neutral signal</param>
        /// <param name="indicatorName">Name of the indicator</param>
        /// <returns>Neutral signal</returns>
        public static IndicatorSignal Neutral(string reason, string indicatorName = "")
        {
            return new IndicatorSignal(SignalDirection.Neutral, 0, reason, indicatorName);
        }

        /// <summary>
        /// Creates a buy signal with specified confidence
        /// </summary>
        /// <param name="confidence">Confidence level</param>
        /// <param name="reason">Reason for buy signal</param>
        /// <param name="indicatorName">Name of the indicator</param>
        /// <param name="rawValue">Raw indicator value</param>
        /// <returns>Buy signal</returns>
        public static IndicatorSignal Buy(decimal confidence, string reason, string indicatorName = "", decimal rawValue = 0)
        {
            return new IndicatorSignal(SignalDirection.Buy, confidence, reason, indicatorName, rawValue);
        }

        /// <summary>
        /// Creates a sell signal with specified confidence
        /// </summary>
        /// <param name="confidence">Confidence level</param>
        /// <param name="reason">Reason for sell signal</param>
        /// <param name="indicatorName">Name of the indicator</param>
        /// <param name="rawValue">Raw indicator value</param>
        /// <returns>Sell signal</returns>
        public static IndicatorSignal Sell(decimal confidence, string reason, string indicatorName = "", decimal rawValue = 0)
        {
            return new IndicatorSignal(SignalDirection.Sell, confidence, reason, indicatorName, rawValue);
        }

        /// <summary>
        /// Gets confidence as percentage (0-100)
        /// </summary>
        public decimal ConfidencePercent => Confidence * 100;

        /// <summary>
        /// Whether this is a tradeable signal (not neutral)
        /// </summary>
        public bool IsTradeable => Direction != SignalDirection.Neutral;

        /// <summary>
        /// Whether this signal meets a minimum confidence threshold
        /// </summary>
        /// <param name="threshold">Minimum confidence (0.0 to 1.0)</param>
        /// <returns>True if signal meets threshold</returns>
        public bool MeetsThreshold(decimal threshold)
        {
            return Confidence >= threshold;
        }

        /// <summary>
        /// String representation of the signal
        /// </summary>
        /// <returns>Formatted signal description</returns>
        public override string ToString()
        {
            var directionSymbol = Direction switch
            {
                SignalDirection.Buy => "📈",
                SignalDirection.Sell => "📉",
                _ => "➡️"
            };

            return $"{directionSymbol} {Direction} @ {ConfidencePercent:F1}% - {Reason}";
        }
    }

    /// <summary>
    /// Represents a candle for indicator calculations
    /// Simplified version of ATAS IndicatorCandle for internal use
    /// </summary>
    public class IndicatorCandle
    {
        /// <summary>
        /// Opening price
        /// </summary>
        public decimal Open { get; set; }

        /// <summary>
        /// Highest price
        /// </summary>
        public decimal High { get; set; }

        /// <summary>
        /// Lowest price
        /// </summary>
        public decimal Low { get; set; }

        /// <summary>
        /// Closing price
        /// </summary>
        public decimal Close { get; set; }

        /// <summary>
        /// Volume traded
        /// </summary>
        public decimal Volume { get; set; }

        /// <summary>
        /// Timestamp of the candle
        /// </summary>
        public DateTime Time { get; set; }

        /// <summary>
        /// Typical price (HLC/3)
        /// </summary>
        public decimal TypicalPrice => (High + Low + Close) / 3;

        /// <summary>
        /// Median price (HL/2)
        /// </summary>
        public decimal MedianPrice => (High + Low) / 2;

        /// <summary>
        /// Weighted close price (HLCC/4)
        /// </summary>
        public decimal WeightedClose => (High + Low + Close + Close) / 4;

        /// <summary>
        /// Price range (High - Low)
        /// </summary>
        public decimal Range => High - Low;

        /// <summary>
        /// Whether this is a bullish candle
        /// </summary>
        public bool IsBullish => Close > Open;

        /// <summary>
        /// Whether this is a bearish candle
        /// </summary>
        public bool IsBearish => Close < Open;

        /// <summary>
        /// Body size of the candle
        /// </summary>
        public decimal BodySize => Math.Abs(Close - Open);

        /// <summary>
        /// Creates a new indicator candle
        /// </summary>
        public IndicatorCandle()
        {
            Time = DateTime.UtcNow;
        }

        /// <summary>
        /// Creates a new indicator candle with specified values
        /// </summary>
        public IndicatorCandle(decimal open, decimal high, decimal low, decimal close, decimal volume, DateTime time = default)
        {
            Open = open;
            High = high;
            Low = low;
            Close = close;
            Volume = volume;
            Time = time == default ? DateTime.UtcNow : time;
        }
    }
}
