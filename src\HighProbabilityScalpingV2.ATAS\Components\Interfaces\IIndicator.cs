using System;
using HighProbabilityScalpingV2.ATAS.Components.Models;

namespace HighProbabilityScalpingV2.ATAS.Components.Interfaces
{
    /// <summary>
    /// Interface for all Phase 1 indicators in the High-Probability Scalping Strategy v2.0
    /// Provides standardized interface for indicator calculations, status reporting, and performance tracking
    /// </summary>
    public interface IIndicator
    {
        /// <summary>
        /// Name of the indicator (e.g., "VFI", "RSI", "BollingerBands")
        /// </summary>
        string Name { get; }

        /// <summary>
        /// Category of the indicator (e.g., "Volume", "Momentum", "Volatility", "Trend")
        /// </summary>
        string Category { get; }

        /// <summary>
        /// Weight of this indicator in signal aggregation (0.0 to 1.0)
        /// </summary>
        decimal Weight { get; set; }

        /// <summary>
        /// Whether this indicator is enabled for calculations
        /// </summary>
        bool IsEnabled { get; set; }

        /// <summary>
        /// Whether the indicator has enough data to generate reliable signals
        /// </summary>
        bool IsReady { get; }

        /// <summary>
        /// Duration of the last calculation for performance monitoring
        /// </summary>
        TimeSpan LastCalculationTime { get; }

        /// <summary>
        /// Calculates indicator signal based on current market data
        /// </summary>
        /// <param name="candle">Current candle data</param>
        /// <param name="marketData">Additional market data (for future use)</param>
        /// <returns>Indicator signal with direction, confidence, and reasoning</returns>
        IndicatorSignal Calculate(IndicatorCandle candle, MarketData marketData);

        /// <summary>
        /// Resets the indicator state and clears all historical data
        /// </summary>
        void Reset();

        /// <summary>
        /// Gets current operational status of the indicator
        /// </summary>
        /// <returns>Status information for monitoring and debugging</returns>
        IndicatorStatus GetStatus();
    }

    /// <summary>
    /// Represents the operational status of an indicator
    /// </summary>
    public class IndicatorStatus
    {
        /// <summary>
        /// Whether the indicator is operational and ready to generate signals
        /// </summary>
        public bool IsOperational { get; set; }

        /// <summary>
        /// Human-readable status message
        /// </summary>
        public string StatusMessage { get; set; } = string.Empty;

        /// <summary>
        /// Number of data points required for the indicator to be ready
        /// </summary>
        public int DataPointsRequired { get; set; }

        /// <summary>
        /// Number of data points currently available
        /// </summary>
        public int DataPointsAvailable { get; set; }

        /// <summary>
        /// Timestamp of the last status update
        /// </summary>
        public DateTime LastUpdate { get; set; }

        /// <summary>
        /// Additional metadata for debugging
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Placeholder for future market data integration
    /// Currently used as a placeholder for Phase 3 order flow data
    /// </summary>
    public class MarketData
    {
        /// <summary>
        /// Current bid price (placeholder)
        /// </summary>
        public decimal Bid { get; set; }

        /// <summary>
        /// Current ask price (placeholder)
        /// </summary>
        public decimal Ask { get; set; }

        /// <summary>
        /// Current spread (placeholder)
        /// </summary>
        public decimal Spread => Ask - Bid;

        /// <summary>
        /// Volume at bid (placeholder for order flow)
        /// </summary>
        public decimal BidVolume { get; set; }

        /// <summary>
        /// Volume at ask (placeholder for order flow)
        /// </summary>
        public decimal AskVolume { get; set; }

        /// <summary>
        /// Additional market data for future phases
        /// </summary>
        public Dictionary<string, object> AdditionalData { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Enumeration for price source selection in indicators
    /// </summary>
    public enum PriceSource
    {
        /// <summary>
        /// Opening price of the candle
        /// </summary>
        Open,

        /// <summary>
        /// Highest price of the candle
        /// </summary>
        High,

        /// <summary>
        /// Lowest price of the candle
        /// </summary>
        Low,

        /// <summary>
        /// Closing price of the candle
        /// </summary>
        Close,

        /// <summary>
        /// Typical price (High + Low + Close) / 3
        /// </summary>
        Typical,

        /// <summary>
        /// Median price (High + Low) / 2
        /// </summary>
        Median,

        /// <summary>
        /// Weighted close (High + Low + Close + Close) / 4
        /// </summary>
        WeightedClose
    }
}
