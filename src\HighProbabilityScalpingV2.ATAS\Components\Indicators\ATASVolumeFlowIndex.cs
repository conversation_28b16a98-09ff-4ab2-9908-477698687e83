using ATAS.Indicators;
using ATAS.Indicators.Drawing;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace HighProbabilityScalpingV2.ATAS.Components.Indicators
{
    /// <summary>
    /// ATAS-Compatible Volume Flow Index Indicator
    /// Wraps our custom VFI logic in ATAS indicator pattern
    /// </summary>
    [DisplayName("Volume Flow Index")]
    public class ATASVolumeFlowIndex : Indicator
    {
        #region Fields
        private readonly ValueDataSeries _vfiSeries;
        private readonly ValueDataSeries _signalSeries;
        
        // Calculation fields
        private int _period = 14;
        private decimal _buyThreshold = 1.3m;
        private decimal _sellThreshold = 0.7m;
        
        // Data storage
        private readonly CircularBuffer<decimal> _typicalPrices;
        private readonly CircularBuffer<decimal> _volumes;
        private readonly CircularBuffer<decimal> _moneyFlow;
        
        private decimal _lastVFI = 0m;
        #endregion

        #region Properties
        [Display(Name = "Period", Order = 10)]
        public int Period
        {
            get => _period;
            set
            {
                _period = Math.Max(1, value);
                RecalculateValues();
            }
        }

        [Display(Name = "Buy Threshold", Order = 20)]
        public decimal BuyThreshold
        {
            get => _buyThreshold;
            set
            {
                _buyThreshold = value;
                RecalculateValues();
            }
        }

        [Display(Name = "Sell Threshold", Order = 30)]
        public decimal SellThreshold
        {
            get => _sellThreshold;
            set
            {
                _sellThreshold = value;
                RecalculateValues();
            }
        }
        #endregion

        #region Constructor
        public ATASVolumeFlowIndex()
            : base(true)
        {
            // Initialize data series for visualization
            _vfiSeries = (ValueDataSeries)DataSeries[0];
            _vfiSeries.Name = "VFI";
            _vfiSeries.Color = System.Windows.Media.Colors.Blue;
            _vfiSeries.VisualType = VisualMode.Line;

            _signalSeries = new ValueDataSeries("Signal")
            {
                Color = System.Windows.Media.Colors.Red,
                VisualType = VisualMode.Histogram
            };
            DataSeries.Add(_signalSeries);

            // Initialize circular buffers
            _typicalPrices = new CircularBuffer<decimal>(_period);
            _volumes = new CircularBuffer<decimal>(_period);
            _moneyFlow = new CircularBuffer<decimal>(_period);
        }
        #endregion

        #region ATAS Indicator Implementation
        protected override void OnCalculate(int bar, decimal value)
        {
            // Get candle data
            var candle = GetCandle(bar);
            if (candle == null) return;

            // Calculate typical price
            var typicalPrice = (candle.High + candle.Low + candle.Close) / 3m;
            var volume = candle.Volume;

            _typicalPrices.Add(typicalPrice);
            _volumes.Add(volume);

            // Calculate money flow
            if (_typicalPrices.Count >= 2)
            {
                var previousTypicalPrice = _typicalPrices[_typicalPrices.Count - 2];
                var rawMoneyFlow = typicalPrice * volume;
                
                // Determine money flow direction
                var moneyFlow = typicalPrice > previousTypicalPrice ? rawMoneyFlow :
                               typicalPrice < previousTypicalPrice ? -rawMoneyFlow : 0;
                
                _moneyFlow.Add(moneyFlow);
            }

            // Calculate VFI if we have enough data
            if (_moneyFlow.Count >= _period)
            {
                var positiveFlow = 0m;
                var negativeFlow = 0m;
                var totalVolume = 0m;

                for (int i = 0; i < _period; i++)
                {
                    var flow = _moneyFlow[_moneyFlow.Count - 1 - i];
                    var vol = _volumes[_volumes.Count - 1 - i];
                    
                    if (flow > 0)
                        positiveFlow += flow;
                    else
                        negativeFlow += Math.Abs(flow);
                    
                    totalVolume += vol;
                }

                // Calculate VFI
                if (totalVolume > 0)
                {
                    var vfi = (positiveFlow - negativeFlow) / totalVolume;
                    _lastVFI = vfi;
                    
                    // Set values for visualization
                    _vfiSeries[bar] = vfi;
                    
                    // Generate signal
                    var signal = vfi >= _buyThreshold ? 1m :
                                vfi <= _sellThreshold ? -1m : 0m;
                    _signalSeries[bar] = signal;
                }
            }
        }

        /// <summary>
        /// Gets the VFI value for a specific bar
        /// </summary>
        public decimal GetVFI(int bar)
        {
            return _vfiSeries[bar];
        }

        /// <summary>
        /// Gets the current VFI value
        /// </summary>
        public decimal CurrentVFI => _lastVFI;

        /// <summary>
        /// Checks if indicator is ready (has enough data)
        /// </summary>
        public bool IsReady => _moneyFlow.Count >= _period;
        #endregion
    }

    /// <summary>
    /// Simple circular buffer implementation for ATAS indicators
    /// </summary>
    public class CircularBuffer<T>
    {
        private readonly T[] _buffer;
        private readonly int _capacity;
        private int _count;
        private int _head;

        public CircularBuffer(int capacity)
        {
            _capacity = capacity;
            _buffer = new T[capacity];
            _count = 0;
            _head = 0;
        }

        public void Add(T item)
        {
            _buffer[_head] = item;
            _head = (_head + 1) % _capacity;
            if (_count < _capacity)
                _count++;
        }

        public T this[int index]
        {
            get
            {
                if (index >= _count)
                    throw new IndexOutOfRangeException();

                var actualIndex = (_head - _count + index + _capacity) % _capacity;
                return _buffer[actualIndex];
            }
        }

        public int Count => _count;
    }
}
