using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using HighProbabilityScalpingV2.ATAS.Components.Indicators;
using HighProbabilityScalpingV2.ATAS.Components.Models;

namespace HighProbabilityScalpingV2.Tests.IndicatorTests
{
    /// <summary>
    /// Unit tests for VolumeFlowIndex indicator
    /// Validates Phase 1 success criteria:
    /// - VFI generates signals >70% confidence
    /// - Performance <5ms per calculation
    /// - Proper signal generation logic
    /// </summary>
    [TestClass]
    public class VolumeFlowIndexTests
    {
        private VolumeFlowIndex _vfi = null!;

        [TestInitialize]
        public void Setup()
        {
            _vfi = new VolumeFlowIndex(period: 3, buyThreshold: 1.0m, sellThreshold: 0.5m);
        }

        [TestMethod]
        public void VFI_WithInsufficientData_ReturnsNeutral()
        {
            // Arrange
            var candle = CreateTestCandle(100, 1000);

            // Act
            var signal = _vfi.Calculate(candle);

            // Assert
            Assert.AreEqual(SignalDirection.Neutral, signal.Direction);
            Assert.AreEqual(0, signal.Confidence);
            Assert.IsTrue(signal.Reason.Contains("Insufficient data"));
            Assert.AreEqual("VFI", signal.IndicatorName);
        }

        [TestMethod]
        public void VFI_WithSufficientData_GeneratesSignals()
        {
            // Arrange - Add enough data to make indicator ready
            IndicatorSignal? lastSignal = null;

            for (int i = 0; i < 5; i++)
            {
                var candle = CreateTestCandle(100 + i * 2, 1000); // Rising prices
                lastSignal = _vfi.Calculate(candle);

                if (i >= 2) // After sufficient data
                {
                    // Assert
                    Assert.IsTrue(_vfi.IsReady, "VFI should be ready after sufficient data");
                    Assert.IsTrue(lastSignal.Confidence >= 0, "Confidence should be non-negative");
                    Assert.IsTrue(lastSignal.Confidence <= 1, "Confidence should not exceed 1.0");
                }
            }

            // The final signal should be valid (may be neutral if trend is not strong enough)
            Assert.IsNotNull(lastSignal);
            Assert.IsTrue(_vfi.IsReady);
        }

        [TestMethod]
        public void VFI_WithStrongUptrend_GeneratesBuySignals()
        {
            // Arrange - Create VFI with lower thresholds for testing
            var vfi = new VolumeFlowIndex(period: 3, buyThreshold: 0.5m, sellThreshold: 0.5m);
            var prices = new decimal[] { 100, 105, 112, 120, 130 };
            IndicatorSignal? lastSignal = null;

            // Act & Debug
            for (int i = 0; i < prices.Length; i++)
            {
                var candle = CreateTestCandle(prices[i], 2000); // Higher volume for stronger signal
                lastSignal = vfi.Calculate(candle);

                // Debug output
                Console.WriteLine($"Price: {prices[i]}, VFI: {lastSignal.RawValue:F3}, Ready: {vfi.IsReady}, Direction: {lastSignal.Direction}");
            }

            // Assert
            Assert.IsNotNull(lastSignal);
            Assert.IsTrue(vfi.IsReady);

            // Debug: Print final VFI value
            Console.WriteLine($"Final VFI Value: {lastSignal.RawValue:F6}");

            // With such a strong uptrend, we should get positive VFI
            Assert.IsTrue(lastSignal.RawValue > 0, $"VFI should be positive for strong uptrend, got {lastSignal.RawValue:F6}");

            // If it generates a buy signal, it should have good confidence
            if (lastSignal.Direction == SignalDirection.Buy)
            {
                Assert.IsTrue(lastSignal.Confidence >= 0.6m, "Buy signal should have at least 60% confidence");
            }
        }

        [TestMethod]
        public void VFI_WithStrongDowntrend_GeneratesSellSignals()
        {
            // Arrange - Create VFI with lower thresholds for testing
            var vfi = new VolumeFlowIndex(period: 3, buyThreshold: 0.5m, sellThreshold: 0.5m);
            var prices = new decimal[] { 130, 120, 112, 105, 100 };
            IndicatorSignal? lastSignal = null;

            // Act
            foreach (var price in prices)
            {
                var candle = CreateTestCandle(price, 2000); // Higher volume for stronger signal
                lastSignal = vfi.Calculate(candle);
            }

            // Assert
            Assert.IsNotNull(lastSignal);
            Assert.IsTrue(vfi.IsReady);

            // With such a strong downtrend, we should get negative VFI
            Assert.IsTrue(lastSignal.RawValue < 0, $"VFI value should be negative for strong downtrend, got {lastSignal.RawValue:F6}");

            // If it generates a sell signal, it should have good confidence
            if (lastSignal.Direction == SignalDirection.Sell)
            {
                Assert.IsTrue(lastSignal.Confidence >= 0.6m, "Sell signal should have at least 60% confidence");
            }
        }

        [TestMethod]
        public void VFI_PerformanceTest_MeetsTargetTime()
        {
            // Arrange - Initialize with data
            for (int i = 0; i < 10; i++)
            {
                _vfi.Calculate(CreateTestCandle(100 + i, 1000));
            }

            // Act - Measure performance of single calculation
            var startTime = DateTime.UtcNow;
            var candle = CreateTestCandle(120, 1000);
            _vfi.Calculate(candle);
            var duration = DateTime.UtcNow - startTime;

            // Assert - Should meet <5ms target
            Assert.IsTrue(duration.TotalMilliseconds < 5, 
                $"VFI calculation took {duration.TotalMilliseconds:F2}ms, target: <5ms");
        }

        [TestMethod]
        public void VFI_MultipleCalculations_MaintainsPerformance()
        {
            // Arrange
            var totalTime = TimeSpan.Zero;
            var calculations = 100;

            // Act - Perform multiple calculations
            for (int i = 0; i < calculations; i++)
            {
                var startTime = DateTime.UtcNow;
                var candle = CreateTestCandle(100 + i % 20, 1000); // Varying prices
                _vfi.Calculate(candle);
                totalTime += DateTime.UtcNow - startTime;
            }

            // Assert
            var averageTime = totalTime.TotalMilliseconds / calculations;
            Assert.IsTrue(averageTime < 5, 
                $"Average VFI calculation time {averageTime:F2}ms exceeds 5ms target");
        }

        [TestMethod]
        public void VFI_HighConfidenceSignals_MeetThreshold()
        {
            // Arrange - Create very strong trend
            var strongUptrend = new decimal[] { 100, 105, 112, 120, 130 };
            
            // Act
            IndicatorSignal? signal = null;
            foreach (var price in strongUptrend)
            {
                var candle = CreateTestCandle(price, 2000); // High volume
                signal = _vfi.Calculate(candle);
            }

            // Assert
            Assert.IsNotNull(signal);
            if (signal.Direction != SignalDirection.Neutral)
            {
                Assert.IsTrue(signal.ConfidencePercent >= 70, 
                    $"Strong trend should generate >70% confidence, got {signal.ConfidencePercent:F1}%");
            }
        }

        [TestMethod]
        public void VFI_SimpleCalculation_DebugTest()
        {
            // Arrange - Simple test with just 3 data points
            var vfi = new VolumeFlowIndex(period: 2, buyThreshold: 0.1m, sellThreshold: 0.1m);

            // Act - Add 3 candles with clear uptrend
            var signal1 = vfi.Calculate(CreateTestCandle(100, 1000)); // First candle
            Console.WriteLine($"Candle 1: Price=100, VFI={signal1.RawValue:F6}, Ready={vfi.IsReady}");

            var signal2 = vfi.Calculate(CreateTestCandle(110, 1000)); // +10 price change
            Console.WriteLine($"Candle 2: Price=110, VFI={signal2.RawValue:F6}, Ready={vfi.IsReady}");

            var signal3 = vfi.Calculate(CreateTestCandle(120, 1000)); // +10 price change
            Console.WriteLine($"Candle 3: Price=120, VFI={signal3.RawValue:F6}, Ready={vfi.IsReady}");

            // Assert - Should have positive VFI for uptrend
            Assert.IsTrue(vfi.IsReady, "VFI should be ready after 3 candles with period=2");
            Assert.IsTrue(signal3.RawValue > 0, $"VFI should be positive for uptrend, got {signal3.RawValue:F6}");
        }

        [TestMethod]
        public void VFI_Configuration_ValidatesParameters()
        {
            // Test invalid period
            Assert.ThrowsException<ArgumentException>(() =>
                new VolumeFlowIndex(period: 0, buyThreshold: 1.0m, sellThreshold: 0.5m));

            // Test invalid buy threshold
            Assert.ThrowsException<ArgumentException>(() =>
                new VolumeFlowIndex(period: 14, buyThreshold: -1.0m, sellThreshold: 0.5m));

            // Test invalid sell threshold
            Assert.ThrowsException<ArgumentException>(() =>
                new VolumeFlowIndex(period: 14, buyThreshold: 1.0m, sellThreshold: -0.5m));
        }

        [TestMethod]
        public void VFI_Reset_ClearsState()
        {
            // Arrange - Add some data
            for (int i = 0; i < 5; i++)
            {
                _vfi.Calculate(CreateTestCandle(100 + i, 1000));
            }
            
            Assert.IsTrue(_vfi.IsReady);
            Assert.IsTrue(_vfi.CalculationCount > 0);

            // Act
            _vfi.Reset();

            // Assert
            Assert.IsFalse(_vfi.IsReady);
            Assert.AreEqual(0, _vfi.CalculationCount);
            Assert.AreEqual(0, _vfi.CurrentValue);
        }

        [TestMethod]
        public void VFI_Status_ProvidesDebugInfo()
        {
            // Arrange - Add enough data to make VFI ready
            for (int i = 0; i < 5; i++)
            {
                _vfi.Calculate(CreateTestCandle(100 + i, 1000));
            }

            // Act
            var status = _vfi.GetStatus();

            // Assert
            Assert.IsNotNull(status);
            Assert.IsTrue(status.IsOperational);
            Assert.IsTrue(status.DataPointsAvailable > 0);
            Assert.IsTrue(status.StatusMessage.Contains("Ready"));
        }

        /// <summary>
        /// Helper method to create test candles
        /// </summary>
        private IndicatorCandle CreateTestCandle(decimal price, decimal volume, DateTime? time = null)
        {
            return new IndicatorCandle
            {
                Open = price - 0.5m,
                High = price + 1,
                Low = price - 1,
                Close = price,
                Volume = volume,
                Time = time ?? DateTime.UtcNow
            };
        }
    }
}
