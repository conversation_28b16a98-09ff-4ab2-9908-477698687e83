using System;
using HighProbabilityScalpingV2.ATAS.Components.Interfaces;
using HighProbabilityScalpingV2.ATAS.Components.Models;
using HighProbabilityScalpingV2.ATAS.Components.Utils;

namespace HighProbabilityScalpingV2.ATAS.Components.Indicators
{
    /// <summary>
    /// Volume Flow Index (VFI) indicator implementation
    /// Optimized for ultra-low timeframe scalping with less than 5ms performance target
    /// Primary indicator for Phase 1 with 30% weight in signal generation
    /// </summary>
    public class VolumeFlowIndex : IIndicator
    {
        private readonly CircularBuffer<decimal> _typicalPrices;
        private readonly CircularBuffer<decimal> _volumes;
        private readonly CircularBuffer<decimal> _vfiValues;
        private readonly int _period;
        private readonly decimal _buyThreshold;
        private readonly decimal _sellThreshold;
        private readonly string _name;

        // Performance tracking
        private DateTime _lastCalculationTime;
        private TimeSpan _lastCalculationDuration;
        private long _calculationCount;

        // IIndicator interface properties
        private decimal _weight = 0.30m; // 30% weight for Phase 1
        private bool _isEnabled = true;

        /// <summary>
        /// Creates a new Volume Flow Index indicator
        /// </summary>
        /// <param name="period">Period for VFI calculation (default: 14)</param>
        /// <param name="buyThreshold">Threshold for buy signals (default: 1.3)</param>
        /// <param name="sellThreshold">Threshold for sell signals (default: 0.7)</param>
        public VolumeFlowIndex(int period = 14, decimal buyThreshold = 1.3m, decimal sellThreshold = 0.7m)
        {
            if (period < 1)
                throw new ArgumentException("Period must be positive", nameof(period));
            if (buyThreshold <= 0)
                throw new ArgumentException("Buy threshold must be positive", nameof(buyThreshold));
            if (sellThreshold <= 0)
                throw new ArgumentException("Sell threshold must be positive", nameof(sellThreshold));

            _period = period;
            _buyThreshold = buyThreshold;
            _sellThreshold = sellThreshold;
            _name = "VFI";

            // Initialize circular buffers
            _typicalPrices = new CircularBuffer<decimal>(period + 1); // +1 for price change calculation
            _volumes = new CircularBuffer<decimal>(period);
            _vfiValues = new CircularBuffer<decimal>(100); // Keep history for trend analysis

            _calculationCount = 0;
        }

        /// <summary>
        /// Calculates VFI value and generates trading signal
        /// </summary>
        /// <param name="candle">Current candle data</param>
        /// <returns>Trading signal with confidence level</returns>
        public IndicatorSignal Calculate(IndicatorCandle candle)
        {
            return Calculate(candle, new MarketData());
        }

        /// <summary>
        /// Calculates VFI value and generates trading signal (IIndicator interface)
        /// </summary>
        /// <param name="candle">Current candle data</param>
        /// <param name="marketData">Additional market data (unused in Phase 1)</param>
        /// <returns>Trading signal with confidence level</returns>
        public IndicatorSignal Calculate(IndicatorCandle candle, MarketData marketData)
        {
            var startTime = DateTime.UtcNow;
            _calculationCount++;

            try
            {
                // Calculate typical price (HLC/3)
                var typicalPrice = candle.TypicalPrice;
                
                // Add to buffers
                _typicalPrices.Add(typicalPrice);
                _volumes.Add(candle.Volume);

                // Check if we have enough data
                if (!IsReady)
                {
                    return IndicatorSignal.Neutral("Insufficient data for VFI calculation", _name);
                }

                // Calculate VFI
                var vfi = CalculateVFI();
                _vfiValues.Add(vfi);

                // Generate signal
                var signal = GenerateSignal(vfi, candle);

                return signal;
            }
            finally
            {
                // Track performance
                _lastCalculationDuration = DateTime.UtcNow - startTime;
                _lastCalculationTime = DateTime.UtcNow;
            }
        }

        /// <summary>
        /// Calculates the Volume Flow Index value
        /// </summary>
        /// <returns>VFI value</returns>
        private decimal CalculateVFI()
        {
            decimal sumVolumePrice = 0;
            decimal sumVolume = 0;

            // Calculate volume-weighted price changes
            // We need at least 2 prices to calculate a price change
            var priceCount = Math.Min(_typicalPrices.Count, _volumes.Count + 1);

            for (int i = 1; i < priceCount; i++) // Start from 1 to calculate price changes
            {
                var currentPrice = _typicalPrices[i];
                var previousPrice = _typicalPrices[i - 1];
                var volume = _volumes[i - 1]; // Volume corresponds to current period

                // Price change direction
                var priceChange = currentPrice - previousPrice;
                var direction = Math.Sign(priceChange);

                // Volume flow calculation
                var volumeFlow = volume * direction;
                sumVolumePrice += volumeFlow;
                sumVolume += volume;
            }

            // Avoid division by zero
            if (sumVolume == 0)
                return 0;

            // VFI = Sum(Volume * Direction) / Sum(Volume)
            return sumVolumePrice / sumVolume;
        }

        /// <summary>
        /// Generates trading signal based on VFI value
        /// </summary>
        /// <param name="vfi">Current VFI value</param>
        /// <param name="candle">Current candle for context</param>
        /// <returns>Trading signal</returns>
        private IndicatorSignal GenerateSignal(decimal vfi, IndicatorCandle candle)
        {
            var reason = $"VFI: {vfi:F3}";

            // Buy signal
            if (vfi > _buyThreshold)
            {
                var confidence = CalculateBuyConfidence(vfi);
                return IndicatorSignal.Buy(confidence, reason, _name, vfi);
            }

            // Sell signal  
            if (vfi < -_sellThreshold)
            {
                var confidence = CalculateSellConfidence(vfi);
                return IndicatorSignal.Sell(confidence, reason, _name, vfi);
            }

            // Neutral signal
            return IndicatorSignal.Neutral(reason, _name);
        }

        /// <summary>
        /// Calculates confidence for buy signals
        /// </summary>
        /// <param name="vfi">Current VFI value</param>
        /// <returns>Confidence level (0.0 to 1.0)</returns>
        private decimal CalculateBuyConfidence(decimal vfi)
        {
            // Base confidence starts at 60% when threshold is met
            var baseConfidence = 0.6m;
            
            // Additional confidence based on how far above threshold
            var excessValue = vfi - _buyThreshold;
            var bonusConfidence = Math.Min(0.35m, excessValue * 0.15m); // Max 35% bonus
            
            var totalConfidence = baseConfidence + bonusConfidence;
            
            // Apply trend boost if available
            if (_vfiValues.Count >= 3)
            {
                var trendBoost = CalculateTrendBoost(true);
                totalConfidence += trendBoost;
            }

            return Math.Min(0.95m, totalConfidence); // Cap at 95%
        }

        /// <summary>
        /// Calculates confidence for sell signals
        /// </summary>
        /// <param name="vfi">Current VFI value</param>
        /// <returns>Confidence level (0.0 to 1.0)</returns>
        private decimal CalculateSellConfidence(decimal vfi)
        {
            // Base confidence starts at 60% when threshold is met
            var baseConfidence = 0.6m;
            
            // Additional confidence based on how far below threshold
            var excessValue = Math.Abs(vfi + _sellThreshold);
            var bonusConfidence = Math.Min(0.35m, excessValue * 0.15m); // Max 35% bonus
            
            var totalConfidence = baseConfidence + bonusConfidence;
            
            // Apply trend boost if available
            if (_vfiValues.Count >= 3)
            {
                var trendBoost = CalculateTrendBoost(false);
                totalConfidence += trendBoost;
            }

            return Math.Min(0.95m, totalConfidence); // Cap at 95%
        }

        /// <summary>
        /// Calculates trend boost for confidence
        /// </summary>
        /// <param name="isBuySignal">Whether this is for a buy signal</param>
        /// <returns>Trend boost value</returns>
        private decimal CalculateTrendBoost(bool isBuySignal)
        {
            if (_vfiValues.Count < 3)
                return 0;

            var current = _vfiValues.Latest;
            var previous = _vfiValues[_vfiValues.Count - 2];
            var beforePrevious = _vfiValues[_vfiValues.Count - 3];

            // Check for consistent trend
            if (isBuySignal)
            {
                // For buy signals, look for increasing VFI
                if (current > previous && previous > beforePrevious)
                    return 0.05m; // 5% boost for uptrend
            }
            else
            {
                // For sell signals, look for decreasing VFI
                if (current < previous && previous < beforePrevious)
                    return 0.05m; // 5% boost for downtrend
            }

            return 0;
        }

        /// <summary>
        /// Whether the indicator has enough data to generate signals
        /// </summary>
        public bool IsReady => _typicalPrices.Count >= _period && _volumes.Count >= _period;

        /// <summary>
        /// Current VFI value (latest calculated)
        /// </summary>
        public decimal CurrentValue => _vfiValues.Count > 0 ? _vfiValues.Latest : 0;

        /// <summary>
        /// Performance metrics
        /// </summary>
        public TimeSpan LastCalculationTime => _lastCalculationDuration;
        public long CalculationCount => _calculationCount;
        public DateTime LastUpdateTime => _lastCalculationTime;

        /// <summary>
        /// Indicator configuration
        /// </summary>
        public int Period => _period;
        public decimal BuyThreshold => _buyThreshold;
        public decimal SellThreshold => _sellThreshold;

        #region IIndicator Interface Implementation

        /// <summary>
        /// Name of the indicator
        /// </summary>
        public string Name => _name;

        /// <summary>
        /// Category of the indicator
        /// </summary>
        public string Category => "Volume";

        /// <summary>
        /// Weight of this indicator in signal aggregation
        /// </summary>
        public decimal Weight
        {
            get => _weight;
            set => _weight = Math.Max(0, Math.Min(1, value));
        }

        /// <summary>
        /// Whether this indicator is enabled for calculations
        /// </summary>
        public bool IsEnabled
        {
            get => _isEnabled;
            set => _isEnabled = value;
        }

        /// <summary>
        /// Gets indicator status for debugging (legacy method)
        /// </summary>
        /// <returns>Status string</returns>
        public string GetStatusString()
        {
            return $"VFI: Ready={IsReady}, Value={CurrentValue:F3}, " +
                   $"Calcs={CalculationCount}, LastTime={LastCalculationTime.TotalMilliseconds:F2}ms";
        }

        /// <summary>
        /// Gets current operational status of the indicator (IIndicator interface)
        /// </summary>
        /// <returns>Status information for monitoring and debugging</returns>
        public IndicatorStatus GetStatus()
        {
            return new IndicatorStatus
            {
                IsOperational = IsEnabled && IsReady,
                StatusMessage = IsReady ? "Ready" : "Insufficient data",
                DataPointsRequired = _period,
                DataPointsAvailable = _typicalPrices.Count,
                LastUpdate = _lastCalculationTime,
                Metadata = new Dictionary<string, object>
                {
                    ["CurrentValue"] = CurrentValue,
                    ["CalculationCount"] = CalculationCount,
                    ["LastCalculationTimeMs"] = LastCalculationTime.TotalMilliseconds,
                    ["BuyThreshold"] = BuyThreshold,
                    ["SellThreshold"] = SellThreshold,
                    ["Weight"] = Weight,
                    ["IsEnabled"] = IsEnabled
                }
            };
        }

        #endregion

        /// <summary>
        /// Resets the indicator state
        /// </summary>
        public void Reset()
        {
            _typicalPrices.Clear();
            _volumes.Clear();
            _vfiValues.Clear();
            _calculationCount = 0;
            _lastCalculationTime = DateTime.MinValue;
            _lastCalculationDuration = TimeSpan.Zero;
        }
    }
}
