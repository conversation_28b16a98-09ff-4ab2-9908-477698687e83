using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using ATAS.Indicators;
using ATAS.Strategies.Chart;
using HighProbabilityScalpingV2.ATAS.Components.Indicators;
using HighProbabilityScalpingV2.ATAS.Components.Interfaces;
using HighProbabilityScalpingV2.ATAS.Components.Logging;
using HighProbabilityScalpingV2.ATAS.Components.Models;
using HighProbabilityScalpingV2.ATAS.Components.Performance;
using HighProbabilityScalpingV2.ATAS.Components.SignalProcessing;

namespace HighProbabilityScalpingV2.ATAS
{
    /// <summary>
    /// High-Probability Scalping Strategy v2.0 - Phase 2 Implementation
    ///
    /// This is a complete rebuild of the trading strategy focusing on:
    /// - ATAS-native design without complex dependency injection
    /// - Multi-indicator system: VFI, Ultra-Fast RSI, Bollinger Bands, Stochastic
    /// - Less than 20ms performance target per calculation
    /// - 85% plus confidence signal generation through weighted aggregation
    /// - Clean, maintainable architecture for incremental expansion
    ///
    /// Phase 2 Success Criteria:
    /// All Phase 1 indicators working plus 3 new indicators
    /// Signal aggregation with weighted voting system
    /// Performance less than 20ms per calculation
    /// 85% plus confidence threshold with 3+ indicator alignment
    /// </summary>
    [Display(Name = "High-Probability Scalping v2.0 - Phase 2")]
    public class HighProbabilityScalpingV2Strategy : ChartStrategy
    {
        #region User Settings

        [Display(Name = "VFI Period", GroupName = "VFI Settings", Order = 1)]
        [Range(5, 50)]
        public int VFIPeriod { get; set; } = 14;

        [Display(Name = "VFI Buy Threshold", GroupName = "VFI Settings", Order = 2)]
        [Range(0.5, 3.0)]
        public decimal VFIBuyThreshold { get; set; } = 1.3m;

        [Display(Name = "VFI Sell Threshold", GroupName = "VFI Settings", Order = 3)]
        [Range(0.1, 1.5)]
        public decimal VFISellThreshold { get; set; } = 0.7m;

        [Display(Name = "RSI Period", GroupName = "RSI Settings", Order = 4)]
        [Range(2, 10)]
        public int RSIPeriod { get; set; } = 3;

        [Display(Name = "RSI Overbought", GroupName = "RSI Settings", Order = 5)]
        [Range(70, 95)]
        public int RSIOverbought { get; set; } = 80;

        [Display(Name = "RSI Oversold", GroupName = "RSI Settings", Order = 6)]
        [Range(5, 30)]
        public int RSIOversold { get; set; } = 20;

        [Display(Name = "BB Period", GroupName = "Bollinger Bands", Order = 7)]
        [Range(10, 30)]
        public int BBPeriod { get; set; } = 20;

        [Display(Name = "BB Std Dev", GroupName = "Bollinger Bands", Order = 8)]
        [Range(1.5, 3.0)]
        public decimal BBStdDev { get; set; } = 2.0m;

        [Display(Name = "Stoch K Period", GroupName = "Stochastic", Order = 9)]
        [Range(5, 20)]
        public int StochKPeriod { get; set; } = 14;

        [Display(Name = "Stoch D Period", GroupName = "Stochastic", Order = 10)]
        [Range(2, 5)]
        public int StochDPeriod { get; set; } = 3;

        [Display(Name = "Min Confidence %", GroupName = "Signal Settings", Order = 15)]
        [Range(70, 95)]
        public int MinConfidence { get; set; } = 85;

        [Display(Name = "Min Indicator Alignment", GroupName = "Signal Settings", Order = 16)]
        [Range(2, 4)]
        public int MinIndicatorAlignment { get; set; } = 3;

        [Display(Name = "Enable Debug Logging", GroupName = "Debug", Order = 20)]
        public bool EnableDebugLogging { get; set; } = true;

        [Display(Name = "Performance Reporting Interval", GroupName = "Debug", Order = 21)]
        [Range(50, 500)]
        public int PerformanceReportInterval { get; set; } = 100;

        #endregion

        #region Private Fields

        // Phase 1 Indicators
        private VolumeFlowIndex? _vfi;

        // Phase 2 Indicators
        private UltraFastRSI? _rsi;
        private BollingerBands? _bollingerBands;
        private StochasticOscillator? _stochastic;

        // Signal Processing
        private SignalAggregator? _signalAggregator;

        // Infrastructure
        private SimpleLogger? _logger;
        private PerformanceMonitor? _performance;

        // Statistics
        private int _barCount;
        private int _signalCount;
        private int _qualifiedSignalCount;
        private int _phase1SignalCount;
        private int _phase2SignalCount;

        #endregion

        #region Strategy Lifecycle

        /// <summary>
        /// Called when strategy starts
        /// </summary>
        protected override void OnStarted()
        {
            try
            {
                InitializeComponents();
                ValidateConfiguration();
                LogStartupInfo();
                
                _logger?.LogInfo("✅ High-Probability Scalping v2.0 started successfully");
            }
            catch (Exception ex)
            {
                var errorMsg = $"❌ Failed to start strategy: {ex.Message}";
                _logger?.LogError(errorMsg, ex);
                
                // Also try to show in ATAS (may not work if logger failed)
                try
                {
                    RaiseShowNotification(errorMsg, "Strategy Error");
                }
                catch
                {
                    // Ignore if ATAS logging also fails
                }
                
                throw;
            }
        }

        /// <summary>
        /// Called when strategy stops
        /// </summary>
        public override void Dispose()
        {
            try
            {
                LogShutdownInfo();
                _logger?.LogShutdown();
            }
            catch (Exception ex)
            {
                // Don't throw during disposal
                try
                {
                    _logger?.LogError("Error during strategy disposal", ex);
                }
                catch
                {
                    // Ignore if logging fails during disposal
                }
            }

            base.Dispose();
        }

        /// <summary>
        /// Main calculation method called for each bar
        /// </summary>
        /// <param name="bar">Bar index</param>
        /// <param name="value">Bar value</param>
        protected override void OnCalculate(int bar, decimal value)
        {
            try
            {
                // Skip if not enough data
                if (bar < VFIPeriod)
                {
                    return;
                }

                _barCount++;

                // Process indicator signal with performance monitoring
                using (_performance?.StartTiming("TotalCalculation"))
                {
                    ProcessIndicatorSignal(bar);
                }

                // Periodic performance reporting
                if (_barCount % PerformanceReportInterval == 0)
                {
                    _performance?.LogQuickSummary();
                }

                // Full performance report every 500 bars
                if (_barCount % 500 == 0)
                {
                    _performance?.LogPerformanceReport();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error in OnCalculate for bar {bar}", ex);
            }
        }

        #endregion

        #region Core Processing

        /// <summary>
        /// Processes aggregated indicator signals for current bar
        /// </summary>
        /// <param name="bar">Current bar index</param>
        private void ProcessIndicatorSignal(int bar)
        {
            try
            {
                IndicatorSignal aggregatedSignal;

                // Calculate aggregated signal with performance monitoring
                using (_performance?.StartTiming("SignalAggregation"))
                {
                    var candle = GetIndicatorCandle(bar);
                    var marketData = new MarketData(); // Placeholder for Phase 3
                    aggregatedSignal = _signalAggregator!.AggregateSignals(candle, marketData);
                }

                _signalCount++;

                // Log aggregated signal
                var confidencePercent = aggregatedSignal.ConfidencePercent;
                _logger?.LogSignal($"🎯 AGGREGATED Signal: {aggregatedSignal.Direction} @ {confidencePercent:F1}% - {aggregatedSignal.Reason}");

                // Log individual indicator contributions
                LogIndividualIndicatorStatus(bar);

                // Process signal
                if (aggregatedSignal.Direction != SignalDirection.Neutral)
                {
                    _phase2SignalCount++;

                    // Check if signal meets confidence threshold
                    if (confidencePercent >= MinConfidence)
                    {
                        _qualifiedSignalCount++;
                        _logger?.LogSignal($"✅ QUALIFIED AGGREGATED Signal: {aggregatedSignal.Direction} @ {confidencePercent:F1}% (threshold: {MinConfidence}%)");

                        // Log signal metadata
                        LogSignalMetadata(aggregatedSignal);

                        // Process qualified signal
                        ProcessQualifiedSignal(aggregatedSignal, bar);
                    }
                    else
                    {
                        _logger?.LogDebug($"⚠️ Aggregated signal below threshold: {confidencePercent:F1}% < {MinConfidence}%");
                    }
                }
                else
                {
                    _logger?.LogDebug($"➡️ Neutral aggregated signal: {aggregatedSignal.Reason}");
                }

                // Update performance counters
                _performance?.IncrementCounter("TotalAggregatedSignals");
                if (aggregatedSignal.Direction != SignalDirection.Neutral)
                {
                    _performance?.IncrementCounter("NonNeutralAggregatedSignals");
                }
                if (aggregatedSignal.ConfidencePercent >= MinConfidence)
                {
                    _performance?.IncrementCounter("QualifiedAggregatedSignals");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error processing aggregated signal for bar {bar}", ex);
            }
        }

        /// <summary>
        /// Logs individual indicator status for debugging
        /// </summary>
        /// <param name="bar">Current bar</param>
        private void LogIndividualIndicatorStatus(int bar)
        {
            try
            {
                if (_logger?.IsDebugEnabled == true)
                {
                    _logger.LogDebug($"📊 Individual Indicator Status (Bar {bar}):");
                    _logger.LogDebug($"   VFI: Ready={_vfi?.IsReady}, Value={_vfi?.CurrentValue:F3}");
                    _logger.LogDebug($"   RSI: Ready={_rsi?.IsReady}, Value={_rsi?.CurrentValue:F1}");
                    _logger.LogDebug($"   BB: Ready={_bollingerBands?.IsReady}, Middle={_bollingerBands?.MiddleBand:F2}");
                    _logger.LogDebug($"   Stoch: Ready={_stochastic?.IsReady}, K={_stochastic?.CurrentK:F1}, D={_stochastic?.CurrentD:F1}");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error logging individual indicator status", ex);
            }
        }

        /// <summary>
        /// Logs detailed signal metadata for analysis
        /// </summary>
        /// <param name="signal">Signal with metadata</param>
        private void LogSignalMetadata(IndicatorSignal signal)
        {
            try
            {
                if (signal.Metadata.Count > 0)
                {
                    _logger?.LogSignal("📋 Signal Metadata:");
                    foreach (var kvp in signal.Metadata)
                    {
                        _logger?.LogSignal($"   {kvp.Key}: {kvp.Value}");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error logging signal metadata", ex);
            }
        }

        /// <summary>
        /// Processes qualified signals that meet confidence threshold
        /// </summary>
        /// <param name="signal">Qualified signal</param>
        /// <param name="bar">Current bar</param>
        private void ProcessQualifiedSignal(IndicatorSignal signal, int bar)
        {
            try
            {
                // Phase 2: Log qualified aggregated signals
                // Future phases will add actual trade execution

                _logger?.LogTrade($"🎯 QUALIFIED AGGREGATED SIGNAL: {signal.Direction} @ {signal.ConfidencePercent:F1}% on bar {bar}");
                _logger?.LogTrade($"   📊 Aggregator: {signal.IndicatorName}");
                _logger?.LogTrade($"   📈 Raw Value: {signal.RawValue:F3}");
                _logger?.LogTrade($"   ⏰ Time: {DateTime.Now:HH:mm:ss}");

                // Extract aggregation details from metadata
                if (signal.Metadata.ContainsKey("BuySignals") && signal.Metadata.ContainsKey("SellSignals"))
                {
                    var buySignals = signal.Metadata["BuySignals"];
                    var sellSignals = signal.Metadata["SellSignals"];
                    var totalIndicators = signal.Metadata.ContainsKey("EnabledIndicators") ? signal.Metadata["EnabledIndicators"] : 0;

                    _logger?.LogTrade($"   🔢 Indicator Alignment: Buy={buySignals}, Sell={sellSignals}, Total={totalIndicators}");
                }

                // Show notification in ATAS (if possible)
                try
                {
                    var message = $"Phase2 {signal.Direction} @ {signal.ConfidencePercent:F1}%";
                    RaiseShowNotification(message, "Signal Alert");
                }
                catch
                {
                    // Ignore if ATAS notification fails
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("Error processing qualified signal", ex);
            }
        }

        /// <summary>
        /// Converts ATAS candle to internal candle format
        /// </summary>
        /// <param name="bar">Bar index</param>
        /// <returns>Internal candle representation</returns>
        private Components.Models.IndicatorCandle GetIndicatorCandle(int bar)
        {
            var atasCandle = GetCandle(bar);
            return new Components.Models.IndicatorCandle
            {
                Open = (decimal)atasCandle.Open,
                High = (decimal)atasCandle.High,
                Low = (decimal)atasCandle.Low,
                Close = (decimal)atasCandle.Close,
                Volume = (decimal)atasCandle.Volume,
                Time = atasCandle.Time
            };
        }

        #endregion

        #region Initialization and Configuration

        /// <summary>
        /// Initializes all strategy components
        /// </summary>
        private void InitializeComponents()
        {
            // Initialize logger first
            _logger = new SimpleLogger("HighProbabilityScalpingV2_Phase2", EnableDebugLogging);

            // Initialize performance monitor
            _performance = new PerformanceMonitor(_logger);

            // Initialize Phase 1 indicators
            _vfi = new VolumeFlowIndex(VFIPeriod, VFIBuyThreshold, VFISellThreshold);

            // Initialize Phase 2 indicators
            _rsi = new UltraFastRSI(RSIPeriod, RSIOverbought, RSIOversold);
            _bollingerBands = new BollingerBands(BBPeriod, BBStdDev);
            _stochastic = new StochasticOscillator(StochKPeriod, StochDPeriod);

            // Initialize signal aggregator
            _signalAggregator = new SignalAggregator(_logger, MinConfidence / 100m, MinIndicatorAlignment);

            // Add all indicators to aggregator
            _signalAggregator.AddIndicator(_vfi);
            _signalAggregator.AddIndicator(_rsi);
            _signalAggregator.AddIndicator(_bollingerBands);
            _signalAggregator.AddIndicator(_stochastic);

            // Reset counters
            _barCount = 0;
            _signalCount = 0;
            _qualifiedSignalCount = 0;
            _phase1SignalCount = 0;
            _phase2SignalCount = 0;

            _logger.LogInfo("✅ All Phase 2 components initialized successfully");
            _logger.LogInfo($"📊 VFI Settings: Period={VFIPeriod}, Buy={VFIBuyThreshold}, Sell={VFISellThreshold}");
            _logger.LogInfo($"📊 RSI Settings: Period={RSIPeriod}, OB={RSIOverbought}, OS={RSIOversold}");
            _logger.LogInfo($"📊 BB Settings: Period={BBPeriod}, StdDev={BBStdDev}");
            _logger.LogInfo($"📊 Stoch Settings: K={StochKPeriod}, D={StochDPeriod}");
            _logger.LogInfo($"🎯 Signal Settings: MinConfidence={MinConfidence}%, MinAlignment={MinIndicatorAlignment}");
            _logger.LogInfo($"📈 Performance Reporting: Every {PerformanceReportInterval} bars");
        }

        /// <summary>
        /// Validates strategy configuration
        /// </summary>
        private void ValidateConfiguration()
        {
            var errors = new List<string>();

            // Validate VFI settings
            if (VFIPeriod < 5 || VFIPeriod > 50)
                errors.Add($"VFI Period {VFIPeriod} outside valid range (5-50)");

            if (VFIBuyThreshold < 0.5m || VFIBuyThreshold > 3.0m)
                errors.Add($"VFI Buy Threshold {VFIBuyThreshold} outside valid range (0.5-3.0)");

            if (VFISellThreshold < 0.1m || VFISellThreshold > 1.5m)
                errors.Add($"VFI Sell Threshold {VFISellThreshold} outside valid range (0.1-1.5)");

            // Validate RSI settings
            if (RSIPeriod < 2 || RSIPeriod > 10)
                errors.Add($"RSI Period {RSIPeriod} outside valid range (2-10)");

            if (RSIOverbought < 70 || RSIOverbought > 95)
                errors.Add($"RSI Overbought {RSIOverbought} outside valid range (70-95)");

            if (RSIOversold < 5 || RSIOversold > 30)
                errors.Add($"RSI Oversold {RSIOversold} outside valid range (5-30)");

            if (RSIOverbought <= RSIOversold)
                errors.Add($"RSI Overbought {RSIOverbought} must be greater than Oversold {RSIOversold}");

            // Validate Bollinger Bands settings
            if (BBPeriod < 10 || BBPeriod > 30)
                errors.Add($"BB Period {BBPeriod} outside valid range (10-30)");

            if (BBStdDev < 1.5m || BBStdDev > 3.0m)
                errors.Add($"BB Std Dev {BBStdDev} outside valid range (1.5-3.0)");

            // Validate Stochastic settings
            if (StochKPeriod < 5 || StochKPeriod > 20)
                errors.Add($"Stoch K Period {StochKPeriod} outside valid range (5-20)");

            if (StochDPeriod < 2 || StochDPeriod > 5)
                errors.Add($"Stoch D Period {StochDPeriod} outside valid range (2-5)");

            // Validate signal settings
            if (MinConfidence < 70 || MinConfidence > 95)
                errors.Add($"Min Confidence {MinConfidence} outside valid range (70-95)");

            if (MinIndicatorAlignment < 2 || MinIndicatorAlignment > 4)
                errors.Add($"Min Indicator Alignment {MinIndicatorAlignment} outside valid range (2-4)");

            // Validate performance settings
            if (PerformanceReportInterval < 50 || PerformanceReportInterval > 500)
                errors.Add($"Performance Report Interval {PerformanceReportInterval} outside valid range (50-500)");

            if (errors.Count > 0)
            {
                var errorMsg = "Configuration errors: " + string.Join(", ", errors);
                _logger?.LogError(errorMsg);
                throw new InvalidOperationException(errorMsg);
            }

            _logger?.LogInfo("✅ Phase 2 configuration validation passed");
        }

        #endregion

        #region Logging and Reporting

        /// <summary>
        /// Logs strategy startup information
        /// </summary>
        private void LogStartupInfo()
        {
            _logger?.LogStartup("v2.0.0", "Phase 2");
            _logger?.LogInfo($"🔧 ATAS Integration: {(Container != null ? "Connected" : "Disconnected")}");
            _logger?.LogInfo($"📊 Instrument: {(InstrumentInfo?.Instrument ?? "Unknown")}");
            _logger?.LogInfo($"⏰ Timeframe: {ChartInfo?.ChartType ?? "Unknown"}");
            _logger?.LogInfo($"🎯 Phase 2 Features: Multi-indicator aggregation, weighted voting, enhanced confidence");
            _logger?.LogInfo($"📈 Indicators: VFI (30%), RSI (25%), BB (20%), Stoch (15%)");
        }

        /// <summary>
        /// Logs strategy shutdown information
        /// </summary>
        private void LogShutdownInfo()
        {
            _logger?.LogSeparator("FINAL PHASE 2 STATISTICS");
            _logger?.LogInfo($"📊 Total Bars Processed: {_barCount:N0}");
            _logger?.LogInfo($"📈 Total Aggregated Signals: {_signalCount:N0}");
            _logger?.LogInfo($"✅ Qualified Signals: {_qualifiedSignalCount:N0}");
            _logger?.LogInfo($"🔄 Phase 2 Signals: {_phase2SignalCount:N0}");

            if (_signalCount > 0)
            {
                var qualificationRate = (decimal)_qualifiedSignalCount / _signalCount * 100;
                _logger?.LogInfo($"🎯 Signal Qualification Rate: {qualificationRate:F1}%");
            }

            // Log individual indicator performance
            if (_vfi != null)
            {
                _logger?.LogInfo($"⚡ VFI Performance: {_vfi.LastCalculationTime.TotalMilliseconds:F2}ms avg, {_vfi.CalculationCount:N0} calcs");
            }
            if (_rsi != null)
            {
                _logger?.LogInfo($"⚡ RSI Performance: {_rsi.LastCalculationTime.TotalMilliseconds:F2}ms avg, {_rsi.CalculationCount:N0} calcs");
            }
            if (_bollingerBands != null)
            {
                _logger?.LogInfo($"⚡ BB Performance: {_bollingerBands.LastCalculationTime.TotalMilliseconds:F2}ms avg, {_bollingerBands.CalculationCount:N0} calcs");
            }
            if (_stochastic != null)
            {
                _logger?.LogInfo($"⚡ Stoch Performance: {_stochastic.LastCalculationTime.TotalMilliseconds:F2}ms avg, {_stochastic.CalculationCount:N0} calcs");
            }

            // Log aggregator statistics
            if (_signalAggregator != null)
            {
                _logger?.LogInfo($"🎯 Aggregator Stats: {_signalAggregator.GetStatistics()}");
            }

            _performance?.LogPerformanceReport();
        }

        #endregion

        #region Public Properties for Monitoring

        /// <summary>
        /// Current VFI value for external monitoring
        /// </summary>
        public decimal CurrentVFIValue => _vfi?.CurrentValue ?? 0;

        /// <summary>
        /// Current RSI value for external monitoring
        /// </summary>
        public decimal CurrentRSIValue => _rsi?.CurrentValue ?? 0;

        /// <summary>
        /// Current Bollinger Bands middle value for external monitoring
        /// </summary>
        public decimal CurrentBBMiddle => _bollingerBands?.MiddleBand ?? 0;

        /// <summary>
        /// Current Stochastic %K value for external monitoring
        /// </summary>
        public decimal CurrentStochK => _stochastic?.CurrentK ?? 0;

        /// <summary>
        /// Whether all Phase 2 indicators are ready
        /// </summary>
        public bool AreAllIndicatorsReady =>
            (_vfi?.IsReady ?? false) &&
            (_rsi?.IsReady ?? false) &&
            (_bollingerBands?.IsReady ?? false) &&
            (_stochastic?.IsReady ?? false);

        /// <summary>
        /// Number of ready indicators
        /// </summary>
        public int ReadyIndicatorCount
        {
            get
            {
                int count = 0;
                if (_vfi?.IsReady == true) count++;
                if (_rsi?.IsReady == true) count++;
                if (_bollingerBands?.IsReady == true) count++;
                if (_stochastic?.IsReady == true) count++;
                return count;
            }
        }

        /// <summary>
        /// Total number of bars processed
        /// </summary>
        public int BarsProcessed => _barCount;

        /// <summary>
        /// Total aggregated signals generated
        /// </summary>
        public int SignalsGenerated => _signalCount;

        /// <summary>
        /// Phase 2 signals generated
        /// </summary>
        public int Phase2SignalsGenerated => _phase2SignalCount;

        /// <summary>
        /// Qualified signals (meeting confidence threshold)
        /// </summary>
        public int QualifiedSignals => _qualifiedSignalCount;

        /// <summary>
        /// Signal qualification rate as percentage
        /// </summary>
        public decimal SignalQualificationRate =>
            _signalCount > 0 ? (decimal)_qualifiedSignalCount / _signalCount * 100 : 0;

        /// <summary>
        /// Average aggregation time in milliseconds
        /// </summary>
        public double AverageAggregationTime =>
            _signalAggregator?.LastAggregationTime.TotalMilliseconds ?? 0;

        /// <summary>
        /// Strategy status for debugging
        /// </summary>
        public string StrategyStatus =>
            $"Phase2: Bars={_barCount}, Signals={_signalCount}, Qualified={_qualifiedSignalCount}, " +
            $"Ready={ReadyIndicatorCount}/4, VFI={(_vfi?.IsReady == true ? "✓" : "✗")}, " +
            $"RSI={(_rsi?.IsReady == true ? "✓" : "✗")}, BB={(_bollingerBands?.IsReady == true ? "✓" : "✗")}, " +
            $"Stoch={(_stochastic?.IsReady == true ? "✓" : "✗")}";

        /// <summary>
        /// Detailed indicator status
        /// </summary>
        public string DetailedIndicatorStatus
        {
            get
            {
                var status = "Phase 2 Indicators:\n";
                status += $"  VFI: {(_vfi?.IsReady == true ? "Ready" : "Not Ready")} - Value: {CurrentVFIValue:F3}\n";
                status += $"  RSI: {(_rsi?.IsReady == true ? "Ready" : "Not Ready")} - Value: {CurrentRSIValue:F1}\n";
                status += $"  BB:  {(_bollingerBands?.IsReady == true ? "Ready" : "Not Ready")} - Middle: {CurrentBBMiddle:F2}\n";
                status += $"  Stoch: {(_stochastic?.IsReady == true ? "Ready" : "Not Ready")} - K: {CurrentStochK:F1}";
                return status;
            }
        }

        #endregion
    }
}
