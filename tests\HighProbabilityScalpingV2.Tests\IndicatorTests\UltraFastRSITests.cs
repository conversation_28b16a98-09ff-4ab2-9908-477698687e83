using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using HighProbabilityScalpingV2.ATAS.Components.Indicators;
using HighProbabilityScalpingV2.ATAS.Components.Interfaces;
using HighProbabilityScalpingV2.ATAS.Components.Models;

namespace HighProbabilityScalpingV2.Tests.IndicatorTests
{
    /// <summary>
    /// Unit tests for UltraFastRSI indicator
    /// Validates Phase 2 success criteria:
    /// - RSI generates signals with proper confidence levels
    /// - Performance <5ms per calculation
    /// - Proper overbought/oversold signal generation
    /// </summary>
    [TestClass]
    public class UltraFastRSITests
    {
        private UltraFastRSI _rsi = null!;

        [TestInitialize]
        public void Setup()
        {
            _rsi = new UltraFastRSI(period: 3, overboughtLevel: 80, oversoldLevel: 20);
        }

        [TestMethod]
        public void RSI_WithInsufficientData_ReturnsNeutral()
        {
            // Arrange
            var candle = CreateTestCandle(100, 1000);
            var marketData = new MarketData();

            // Act
            var signal = _rsi.Calculate(candle, marketData);

            // Assert
            Assert.AreEqual(SignalDirection.Neutral, signal.Direction);
            Assert.AreEqual(0, signal.Confidence);
            Assert.IsTrue(signal.Reason.Contains("Insufficient data"));
            Assert.AreEqual("UltraFastRSI", signal.IndicatorName);
        }

        [TestMethod]
        public void RSI_WithSufficientData_CalculatesCorrectly()
        {
            // Arrange - Add enough data to make indicator ready
            var prices = new decimal[] { 100, 105, 110, 115 }; // Rising prices
            IndicatorSignal? lastSignal = null;

            // Act
            foreach (var price in prices)
            {
                var candle = CreateTestCandle(price, 1000);
                lastSignal = _rsi.Calculate(candle, new MarketData());
            }

            // Assert
            Assert.IsNotNull(lastSignal);
            Assert.IsTrue(_rsi.IsReady);
            Assert.IsTrue(lastSignal.Confidence >= 0);
            Assert.IsTrue(lastSignal.Confidence <= 1);
            Assert.IsTrue(_rsi.CurrentValue >= 0 && _rsi.CurrentValue <= 100);
        }

        [TestMethod]
        public void RSI_WithStrongUptrend_GeneratesHighRSI()
        {
            // Arrange - Create strong uptrend
            var prices = new decimal[] { 100, 110, 120, 130, 140 };
            IndicatorSignal? lastSignal = null;

            // Act
            foreach (var price in prices)
            {
                var candle = CreateTestCandle(price, 1000);
                lastSignal = _rsi.Calculate(candle, new MarketData());
            }

            // Assert
            Assert.IsNotNull(lastSignal);
            Assert.IsTrue(_rsi.IsReady);
            Assert.IsTrue(_rsi.CurrentValue > 50, $"RSI should be > 50 for uptrend, got {_rsi.CurrentValue:F1}");
        }

        [TestMethod]
        public void RSI_WithStrongDowntrend_GeneratesLowRSI()
        {
            // Arrange - Create strong downtrend
            var prices = new decimal[] { 140, 130, 120, 110, 100 };
            IndicatorSignal? lastSignal = null;

            // Act
            foreach (var price in prices)
            {
                var candle = CreateTestCandle(price, 1000);
                lastSignal = _rsi.Calculate(candle, new MarketData());
            }

            // Assert
            Assert.IsNotNull(lastSignal);
            Assert.IsTrue(_rsi.IsReady);
            Assert.IsTrue(_rsi.CurrentValue < 50, $"RSI should be < 50 for downtrend, got {_rsi.CurrentValue:F1}");
        }

        [TestMethod]
        public void RSI_WithOverboughtCondition_GeneratesSellSignal()
        {
            // Arrange - Create very strong uptrend to reach overbought
            var rsi = new UltraFastRSI(period: 3, overboughtLevel: 70, oversoldLevel: 30); // Lower threshold for testing
            var prices = new decimal[] { 100, 115, 130, 145, 160 };
            IndicatorSignal? lastSignal = null;

            // Act
            foreach (var price in prices)
            {
                var candle = CreateTestCandle(price, 1000);
                lastSignal = rsi.Calculate(candle, new MarketData());
            }

            // Assert
            Assert.IsNotNull(lastSignal);
            Assert.IsTrue(rsi.IsReady);
            
            // Should generate sell signal or at least high RSI
            if (lastSignal.Direction == SignalDirection.Sell)
            {
                Assert.IsTrue(lastSignal.Confidence >= 0.6m, "Sell signal should have at least 60% confidence");
            }
            Assert.IsTrue(rsi.CurrentValue >= 70, $"RSI should be >= 70 for overbought, got {rsi.CurrentValue:F1}");
        }

        [TestMethod]
        public void RSI_WithOversoldCondition_GeneratesBuySignal()
        {
            // Arrange - Create very strong downtrend to reach oversold
            var rsi = new UltraFastRSI(period: 3, overboughtLevel: 70, oversoldLevel: 30); // Higher threshold for testing
            var prices = new decimal[] { 160, 145, 130, 115, 100 };
            IndicatorSignal? lastSignal = null;

            // Act
            foreach (var price in prices)
            {
                var candle = CreateTestCandle(price, 1000);
                lastSignal = rsi.Calculate(candle, new MarketData());
            }

            // Assert
            Assert.IsNotNull(lastSignal);
            Assert.IsTrue(rsi.IsReady);
            
            // Should generate buy signal or at least low RSI
            if (lastSignal.Direction == SignalDirection.Buy)
            {
                Assert.IsTrue(lastSignal.Confidence >= 0.6m, "Buy signal should have at least 60% confidence");
            }
            Assert.IsTrue(rsi.CurrentValue <= 30, $"RSI should be <= 30 for oversold, got {rsi.CurrentValue:F1}");
        }

        [TestMethod]
        public void RSI_PerformanceTest_MeetsTargetTime()
        {
            // Arrange - Initialize with data
            for (int i = 0; i < 10; i++)
            {
                _rsi.Calculate(CreateTestCandle(100 + i, 1000), new MarketData());
            }

            // Act - Measure performance of single calculation
            var startTime = DateTime.UtcNow;
            var candle = CreateTestCandle(120, 1000);
            _rsi.Calculate(candle, new MarketData());
            var duration = DateTime.UtcNow - startTime;

            // Assert - Should meet <5ms target
            Assert.IsTrue(duration.TotalMilliseconds < 5, 
                $"RSI calculation took {duration.TotalMilliseconds:F2}ms, target: <5ms");
        }

        [TestMethod]
        public void RSI_InterfaceImplementation_WorksCorrectly()
        {
            // Arrange
            IIndicator indicator = _rsi;

            // Act & Assert
            Assert.AreEqual("UltraFastRSI", indicator.Name);
            Assert.AreEqual("Momentum", indicator.Category);
            Assert.AreEqual(0.25m, indicator.Weight); // 25% weight for Phase 2
            Assert.IsTrue(indicator.IsEnabled);
            Assert.IsFalse(indicator.IsReady); // Not ready initially

            // Test status
            var status = indicator.GetStatus();
            Assert.IsNotNull(status);
            Assert.IsFalse(status.IsOperational); // Not ready yet
        }

        [TestMethod]
        public void RSI_Reset_ClearsState()
        {
            // Arrange - Add some data
            for (int i = 0; i < 5; i++)
            {
                _rsi.Calculate(CreateTestCandle(100 + i, 1000), new MarketData());
            }
            
            Assert.IsTrue(_rsi.IsReady);
            Assert.IsTrue(_rsi.CalculationCount > 0);

            // Act
            _rsi.Reset();

            // Assert
            Assert.IsFalse(_rsi.IsReady);
            Assert.AreEqual(0, _rsi.CalculationCount);
            Assert.AreEqual(0, _rsi.CurrentValue);
        }

        [TestMethod]
        public void RSI_Configuration_ValidatesParameters()
        {
            // Test invalid period
            Assert.ThrowsException<ArgumentException>(() => 
                new UltraFastRSI(period: 1, overboughtLevel: 80, oversoldLevel: 20));

            // Test invalid overbought/oversold levels
            Assert.ThrowsException<ArgumentException>(() => 
                new UltraFastRSI(period: 3, overboughtLevel: 20, oversoldLevel: 80)); // Reversed

            // Test out of range levels
            Assert.ThrowsException<ArgumentException>(() => 
                new UltraFastRSI(period: 3, overboughtLevel: 110, oversoldLevel: 20)); // > 100
        }

        [TestMethod]
        public void RSI_Metadata_ContainsCorrectInformation()
        {
            // Arrange
            for (int i = 0; i < 5; i++)
            {
                _rsi.Calculate(CreateTestCandle(100 + i * 5, 1000), new MarketData());
            }

            // Act
            var signal = _rsi.Calculate(CreateTestCandle(130, 1000), new MarketData());

            // Assert
            Assert.IsTrue(signal.Metadata.ContainsKey("RSI"));
            Assert.IsTrue(signal.Metadata.ContainsKey("Period"));
            Assert.IsTrue(signal.Metadata.ContainsKey("OverboughtLevel"));
            Assert.IsTrue(signal.Metadata.ContainsKey("OversoldLevel"));
            Assert.IsTrue(signal.Metadata.ContainsKey("PriceSource"));
            
            Assert.AreEqual(3, signal.Metadata["Period"]);
            Assert.AreEqual(80m, signal.Metadata["OverboughtLevel"]);
            Assert.AreEqual(20m, signal.Metadata["OversoldLevel"]);
        }

        /// <summary>
        /// Helper method to create test candles
        /// </summary>
        private IndicatorCandle CreateTestCandle(decimal price, decimal volume, DateTime? time = null)
        {
            return new IndicatorCandle
            {
                Open = price - 0.5m,
                High = price + 1,
                Low = price - 1,
                Close = price,
                Volume = volume,
                Time = time ?? DateTime.UtcNow
            };
        }
    }
}
