using System;
using System.Collections.Generic;
using System.Linq;
using HighProbabilityScalpingV2.ATAS.Components.Interfaces;
using HighProbabilityScalpingV2.ATAS.Components.Models;
using HighProbabilityScalpingV2.ATAS.Components.Utils;

namespace HighProbabilityScalpingV2.ATAS.Components.Indicators
{
    /// <summary>
    /// Bollinger Bands indicator implementation
    /// 20-period moving average with 2 standard deviation bands
    /// Phase 2 indicator with 20% weight in signal generation
    /// </summary>
    public class BollingerBands : IIndicator
    {
        private readonly CircularBuffer<decimal> _prices;
        private readonly CircularBuffer<decimal> _middleBand; // Moving Average
        private readonly CircularBuffer<decimal> _upperBand;
        private readonly CircularBuffer<decimal> _lowerBand;
        private readonly CircularBuffer<decimal> _bandwidths;
        private readonly int _period;
        private readonly decimal _standardDeviations;
        private readonly PriceSource _priceSource;

        // Performance tracking
        private DateTime _lastCalculationTime;
        private TimeSpan _lastCalculationDuration;
        private long _calculationCount;

        // IIndicator interface properties
        private decimal _weight = 0.20m; // 20% weight for Phase 2
        private bool _isEnabled = true;

        /// <summary>
        /// Creates a new Bollinger Bands indicator
        /// </summary>
        /// <param name="period">Period for moving average calculation (default: 20)</param>
        /// <param name="standardDeviations">Number of standard deviations for bands (default: 2.0)</param>
        /// <param name="priceSource">Price source for calculation (default: Close)</param>
        public BollingerBands(int period = 20, decimal standardDeviations = 2.0m, 
            PriceSource priceSource = PriceSource.Close)
        {
            if (period < 2)
                throw new ArgumentException("Period must be at least 2", nameof(period));
            if (standardDeviations <= 0)
                throw new ArgumentException("Standard deviations must be positive", nameof(standardDeviations));

            _period = period;
            _standardDeviations = standardDeviations;
            _priceSource = priceSource;

            // Initialize circular buffers
            _prices = new CircularBuffer<decimal>(period);
            _middleBand = new CircularBuffer<decimal>(100); // Keep history
            _upperBand = new CircularBuffer<decimal>(100);
            _lowerBand = new CircularBuffer<decimal>(100);
            _bandwidths = new CircularBuffer<decimal>(100);

            _calculationCount = 0;
        }

        /// <summary>
        /// Calculates Bollinger Bands and generates trading signal (IIndicator interface)
        /// </summary>
        /// <param name="candle">Current candle data</param>
        /// <param name="marketData">Additional market data (unused in Phase 2)</param>
        /// <returns>Trading signal with confidence level</returns>
        public IndicatorSignal Calculate(IndicatorCandle candle, MarketData marketData)
        {
            var startTime = DateTime.UtcNow;
            _calculationCount++;

            try
            {
                if (!IsEnabled)
                {
                    return IndicatorSignal.Neutral("Indicator disabled", Name);
                }

                // Get price based on source
                var price = GetPrice(candle, _priceSource);
                _prices.Add(price);

                // Check if we have enough data
                if (!IsReady)
                {
                    return IndicatorSignal.Neutral("Insufficient data for Bollinger Bands calculation", Name);
                }

                // Calculate Bollinger Bands
                var (middle, upper, lower, bandwidth) = CalculateBands();
                
                // Store calculated values
                _middleBand.Add(middle);
                _upperBand.Add(upper);
                _lowerBand.Add(lower);
                _bandwidths.Add(bandwidth);

                // Generate signal
                var signal = GenerateSignal(price, middle, upper, lower, bandwidth, candle);

                // Add metadata
                signal.Metadata["Price"] = price;
                signal.Metadata["MiddleBand"] = middle;
                signal.Metadata["UpperBand"] = upper;
                signal.Metadata["LowerBand"] = lower;
                signal.Metadata["Bandwidth"] = bandwidth;
                signal.Metadata["Period"] = _period;
                signal.Metadata["StandardDeviations"] = _standardDeviations;
                signal.Metadata["PriceSource"] = _priceSource.ToString();

                return signal;
            }
            finally
            {
                // Track performance
                _lastCalculationDuration = DateTime.UtcNow - startTime;
                _lastCalculationTime = DateTime.UtcNow;
            }
        }

        /// <summary>
        /// Calculates the Bollinger Bands values
        /// </summary>
        /// <returns>Tuple of (middle, upper, lower, bandwidth)</returns>
        private (decimal middle, decimal upper, decimal lower, decimal bandwidth) CalculateBands()
        {
            // Calculate moving average (middle band)
            var middle = _prices.Average();

            // Calculate standard deviation
            var variance = 0m;
            for (int i = 0; i < _prices.Count; i++)
            {
                var deviation = _prices[i] - middle;
                variance += deviation * deviation;
            }
            variance /= _prices.Count;
            var standardDeviation = (decimal)Math.Sqrt((double)variance);

            // Calculate upper and lower bands
            var bandOffset = _standardDeviations * standardDeviation;
            var upper = middle + bandOffset;
            var lower = middle - bandOffset;

            // Calculate bandwidth (normalized band width)
            var bandwidth = upper != 0 ? (upper - lower) / middle * 100 : 0;

            return (middle, upper, lower, bandwidth);
        }

        /// <summary>
        /// Generates trading signal based on Bollinger Bands
        /// </summary>
        /// <param name="price">Current price</param>
        /// <param name="middle">Middle band (MA)</param>
        /// <param name="upper">Upper band</param>
        /// <param name="lower">Lower band</param>
        /// <param name="bandwidth">Current bandwidth</param>
        /// <param name="candle">Current candle for context</param>
        /// <returns>Trading signal</returns>
        private IndicatorSignal GenerateSignal(decimal price, decimal middle, decimal upper, decimal lower, 
            decimal bandwidth, IndicatorCandle candle)
        {
            var reason = $"BB: Price={price:F2}, Upper={upper:F2}, Lower={lower:F2}";

            // Calculate position within bands (0 = lower band, 1 = upper band)
            var bandPosition = upper != lower ? (price - lower) / (upper - lower) : 0.5m;

            // Buy signal (price near lower band - oversold)
            if (bandPosition <= 0.2m) // Within 20% of lower band
            {
                var confidence = CalculateOversoldConfidence(bandPosition, bandwidth);
                return IndicatorSignal.Buy(confidence, reason, Name, bandPosition);
            }

            // Sell signal (price near upper band - overbought)
            if (bandPosition >= 0.8m) // Within 20% of upper band
            {
                var confidence = CalculateOverboughtConfidence(bandPosition, bandwidth);
                return IndicatorSignal.Sell(confidence, reason, Name, bandPosition);
            }

            // Mean reversion signals when price is moving back toward middle
            if (bandPosition > 0.2m && bandPosition < 0.5m && _middleBand.Count >= 2)
            {
                // Price moving up from lower band toward middle
                var previousMiddle = _middleBand[_middleBand.Count - 2];
                if (price > previousMiddle)
                {
                    var confidence = CalculateMeanReversionConfidence(bandPosition, true);
                    return IndicatorSignal.Buy(confidence, $"{reason} (Mean Reversion Up)", Name, bandPosition);
                }
            }

            if (bandPosition < 0.8m && bandPosition > 0.5m && _middleBand.Count >= 2)
            {
                // Price moving down from upper band toward middle
                var previousMiddle = _middleBand[_middleBand.Count - 2];
                if (price < previousMiddle)
                {
                    var confidence = CalculateMeanReversionConfidence(bandPosition, false);
                    return IndicatorSignal.Sell(confidence, $"{reason} (Mean Reversion Down)", Name, bandPosition);
                }
            }

            // Neutral signal
            return IndicatorSignal.Neutral(reason, Name);
        }

        /// <summary>
        /// Calculates confidence for oversold (buy) signals
        /// </summary>
        /// <param name="bandPosition">Position within bands (0-1)</param>
        /// <param name="bandwidth">Current bandwidth</param>
        /// <returns>Confidence level (0.0 to 1.0)</returns>
        private decimal CalculateOversoldConfidence(decimal bandPosition, decimal bandwidth)
        {
            // Base confidence starts at 60%
            var baseConfidence = 0.6m;
            
            // Higher confidence for prices closer to lower band
            var proximityBonus = (0.2m - bandPosition) * 1.5m; // Max 30% bonus
            
            // Higher confidence when bands are wider (more volatile)
            var volatilityBonus = Math.Min(0.1m, bandwidth * 0.01m); // Max 10% bonus
            
            var totalConfidence = baseConfidence + proximityBonus + volatilityBonus;
            
            return Math.Min(0.95m, Math.Max(0.6m, totalConfidence));
        }

        /// <summary>
        /// Calculates confidence for overbought (sell) signals
        /// </summary>
        /// <param name="bandPosition">Position within bands (0-1)</param>
        /// <param name="bandwidth">Current bandwidth</param>
        /// <returns>Confidence level (0.0 to 1.0)</returns>
        private decimal CalculateOverboughtConfidence(decimal bandPosition, decimal bandwidth)
        {
            // Base confidence starts at 60%
            var baseConfidence = 0.6m;
            
            // Higher confidence for prices closer to upper band
            var proximityBonus = (bandPosition - 0.8m) * 1.5m; // Max 30% bonus
            
            // Higher confidence when bands are wider (more volatile)
            var volatilityBonus = Math.Min(0.1m, bandwidth * 0.01m); // Max 10% bonus
            
            var totalConfidence = baseConfidence + proximityBonus + volatilityBonus;
            
            return Math.Min(0.95m, Math.Max(0.6m, totalConfidence));
        }

        /// <summary>
        /// Calculates confidence for mean reversion signals
        /// </summary>
        /// <param name="bandPosition">Position within bands (0-1)</param>
        /// <param name="isBuySignal">Whether this is a buy signal</param>
        /// <returns>Confidence level (0.0 to 1.0)</returns>
        private decimal CalculateMeanReversionConfidence(decimal bandPosition, bool isBuySignal)
        {
            // Lower base confidence for mean reversion signals
            var baseConfidence = 0.5m;
            
            // Bonus based on how far from middle band
            var distanceFromMiddle = Math.Abs(bandPosition - 0.5m);
            var distanceBonus = distanceFromMiddle * 0.4m; // Max 20% bonus
            
            var totalConfidence = baseConfidence + distanceBonus;
            
            return Math.Min(0.75m, Math.Max(0.5m, totalConfidence)); // Cap at 75% for mean reversion
        }

        /// <summary>
        /// Gets price value based on specified source
        /// </summary>
        /// <param name="candle">Candle data</param>
        /// <param name="source">Price source</param>
        /// <returns>Price value</returns>
        private decimal GetPrice(IndicatorCandle candle, PriceSource source)
        {
            return source switch
            {
                PriceSource.Open => candle.Open,
                PriceSource.High => candle.High,
                PriceSource.Low => candle.Low,
                PriceSource.Close => candle.Close,
                PriceSource.Typical => candle.TypicalPrice,
                PriceSource.Median => candle.MedianPrice,
                PriceSource.WeightedClose => candle.WeightedClose,
                _ => candle.Close
            };
        }

        /// <summary>
        /// Resets the indicator state and clears all historical data
        /// </summary>
        public void Reset()
        {
            _prices.Clear();
            _middleBand.Clear();
            _upperBand.Clear();
            _lowerBand.Clear();
            _bandwidths.Clear();
            _calculationCount = 0;
            _lastCalculationTime = DateTime.MinValue;
            _lastCalculationDuration = TimeSpan.Zero;
        }

        #region IIndicator Interface Implementation

        /// <summary>
        /// Name of the indicator
        /// </summary>
        public string Name => "BollingerBands";

        /// <summary>
        /// Category of the indicator
        /// </summary>
        public string Category => "Volatility";

        /// <summary>
        /// Weight of this indicator in signal aggregation
        /// </summary>
        public decimal Weight 
        { 
            get => _weight; 
            set => _weight = Math.Max(0, Math.Min(1, value)); 
        }

        /// <summary>
        /// Whether this indicator is enabled for calculations
        /// </summary>
        public bool IsEnabled 
        { 
            get => _isEnabled; 
            set => _isEnabled = value; 
        }

        /// <summary>
        /// Whether the indicator has enough data to generate reliable signals
        /// </summary>
        public bool IsReady => _prices.Count >= _period;

        /// <summary>
        /// Duration of the last calculation for performance monitoring
        /// </summary>
        public TimeSpan LastCalculationTime => _lastCalculationDuration;

        /// <summary>
        /// Gets current operational status of the indicator
        /// </summary>
        /// <returns>Status information for monitoring and debugging</returns>
        public IndicatorStatus GetStatus()
        {
            return new IndicatorStatus
            {
                IsOperational = IsEnabled && IsReady,
                StatusMessage = IsReady ? "Ready" : "Insufficient data",
                DataPointsRequired = _period,
                DataPointsAvailable = _prices.Count,
                LastUpdate = _lastCalculationTime,
                Metadata = new Dictionary<string, object>
                {
                    ["MiddleBand"] = _middleBand.Count > 0 ? _middleBand.Latest : 0,
                    ["UpperBand"] = _upperBand.Count > 0 ? _upperBand.Latest : 0,
                    ["LowerBand"] = _lowerBand.Count > 0 ? _lowerBand.Latest : 0,
                    ["Bandwidth"] = _bandwidths.Count > 0 ? _bandwidths.Latest : 0,
                    ["CalculationCount"] = _calculationCount,
                    ["LastCalculationTimeMs"] = LastCalculationTime.TotalMilliseconds,
                    ["Period"] = _period,
                    ["StandardDeviations"] = _standardDeviations,
                    ["PriceSource"] = _priceSource.ToString(),
                    ["Weight"] = Weight,
                    ["IsEnabled"] = IsEnabled
                }
            };
        }

        #endregion

        #region Public Properties

        /// <summary>
        /// Current middle band value (moving average)
        /// </summary>
        public decimal MiddleBand => _middleBand.Count > 0 ? _middleBand.Latest : 0;

        /// <summary>
        /// Current upper band value
        /// </summary>
        public decimal UpperBand => _upperBand.Count > 0 ? _upperBand.Latest : 0;

        /// <summary>
        /// Current lower band value
        /// </summary>
        public decimal LowerBand => _lowerBand.Count > 0 ? _lowerBand.Latest : 0;

        /// <summary>
        /// Current bandwidth (volatility measure)
        /// </summary>
        public decimal Bandwidth => _bandwidths.Count > 0 ? _bandwidths.Latest : 0;

        /// <summary>
        /// Number of calculations performed
        /// </summary>
        public long CalculationCount => _calculationCount;

        /// <summary>
        /// Timestamp of last update
        /// </summary>
        public DateTime LastUpdateTime => _lastCalculationTime;

        /// <summary>
        /// Indicator configuration
        /// </summary>
        public int Period => _period;
        public decimal StandardDeviations => _standardDeviations;
        public PriceSource PriceSource => _priceSource;

        #endregion
    }
}
